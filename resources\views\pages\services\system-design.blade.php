@extends('layouts.app')

@section('title', 'System Design Services - Scalable Architecture & Infrastructure Solutions')
@section('meta_description', 'Expert system design services including scalable architecture, microservices, cloud infrastructure, high-performance systems, and distributed systems design for enterprise applications.')
@section('meta_keywords', 'system design, scalable architecture, microservices, cloud infrastructure, distributed systems, high-performance systems, enterprise architecture, system architecture')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="text-center lg:text-left">
                <h1 class="heading-1 text-white mb-6">
                    Enterprise <span class="text-blue-300">System Design</span> Services
                </h1>
                <p class="text-lead text-white mb-8 opacity-90">
                    Build scalable, reliable, and high-performance systems that grow with your business. Our expert system architects design robust infrastructure solutions for modern applications.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="btn-primary bg-white text-blue-600 font-semibold hover:bg-gray-50 hover:shadow-lg transition-all duration-300">
                        Get Architecture Consultation
                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="btn-outline bg-white border-white border-2 text-white font-semibold hover:bg-white hover:text-blue-600 hover:shadow-lg transition-all duration-300">
                        View System Projects
                    </a>
                </div>
            </div>
            
            <div class="relative">
                <div class="relative z-10">
                    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-8">
                        <div class="space-y-4">
                            <!-- System Architecture Diagram -->
                            <div class="bg-gray-800 rounded p-4 text-green-400 font-mono text-sm">
                                <div class="flex items-center space-x-2 mb-3">
                                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                    <span class="text-gray-400 ml-2">System Architecture</span>
                                </div>
                                <div class="space-y-1">
                                    <div>┌─ Load Balancer</div>
                                    <div>├─ API Gateway</div>
                                    <div>├─ Microservices</div>
                                    <div>│  ├─ User Service</div>
                                    <div>│  ├─ Auth Service</div>
                                    <div>│  └─ Data Service</div>
                                    <div>├─ Message Queue</div>
                                    <div>├─ Cache Layer</div>
                                    <div>└─ Database Cluster</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- What is System Design -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                What is <span class="text-blue-600">System Design?</span>
            </h2>
            <p class="text-lead max-w-4xl mx-auto">
                System design refers to the architecture and structure of a system, application, or platform. It focuses on how components interact, scale, and perform efficiently to create a blueprint for large-scale, maintainable systems.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- High-level Architecture -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">High-level Architecture</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Deciding how different components (databases, servers, APIs, services) work together and communicate through REST APIs, message queues, and microservices.
                </p>
            </div>
            
            <!-- Scalability -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 6a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 14a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Scalability</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Ensuring systems can handle increasing data and users through horizontal/vertical scaling, load balancing, caching strategies, and data partitioning.
                </p>
            </div>
            
            <!-- Reliability & Availability -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Reliability & Availability</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Designing fault-tolerant systems with minimal downtime using replication, backup systems, and failover mechanisms for high availability.
                </p>
            </div>
            
            <!-- Performance Optimization -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Performance Optimization</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Ensuring systems handle high traffic with fast response times through optimization techniques, caching, and efficient algorithms.
                </p>
            </div>
            
            <!-- Security -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Security</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Implementing comprehensive security strategies including encryption, authentication, authorization, and secure communication protocols.
                </p>
            </div>
            
            <!-- Data Flow and Storage -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V12a1 1 0 102 0V8.414l2.293-2.293A1 1 0 0114 6h2a1 1 0 110 2h-1.586l-2.293 2.293a1 1 0 00-.293.707V15a1 1 0 11-2 0v-3.586l-2.293-2.293A1 1 0 017 9H4a1 1 0 110-2h1.586l2.293-2.293A1 1 0 018 4.414V1a1 1 0 112 0v2.586l2.293 2.293A1 1 0 0113 6h3a1 1 0 110 2h-1.586l-2.293 2.293A1 1 0 0012 10.414V13a1 1 0 11-2 0v-1.586l-2.293-2.293A1 1 0 017 9H4a1 1 0 110-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Data Flow and Storage</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Designing efficient data storage, processing, and retrieval systems including database design and choosing between SQL vs NoSQL technologies.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- System Design Example -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                System Design <span class="text-blue-600">Example</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                Here's how we approach designing a complex e-commerce platform that can handle millions of users simultaneously.
            </p>
        </div>
        
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-8 card-hover">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">E-commerce Platform Architecture</h3>
                <p class="text-gray-600 mb-6">
                    Designing an e-commerce platform involves considering how users' shopping carts, product databases, payment systems, and recommendation engines work together to ensure smooth and scalable performance, even with millions of simultaneous users.
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Key Components:</h4>
                        <ul class="space-y-2 text-gray-600 text-sm">
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Load balancers for traffic distribution
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Microservices for user management
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Distributed product catalog
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Real-time inventory management
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Secure payment processing
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Scalability Features:</h4>
                        <ul class="space-y-2 text-gray-600 text-sm">
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Auto-scaling server clusters
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Redis caching layers
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Database sharding strategies
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                CDN for global content delivery
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Message queues for async processing
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Our System Design Tools -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Our <span class="text-blue-600">System Design Tools</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                We use industry-leading tools and technologies to design, model, and implement robust system architectures.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Architecture Modeling -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Architecture Modeling</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Using tools like Lucidchart, Draw.io, and Visio to create detailed system architecture diagrams and data flow models.
                </p>
            </div>

            <!-- Cloud Platforms -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Cloud Platforms</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Expertise in AWS, Azure, and Google Cloud Platform for scalable cloud infrastructure design and deployment.
                </p>
            </div>

            <!-- Containerization -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 6.707 6.293a1 1 0 00-1.414 1.414L8.586 11l-3.293 3.293a1 1 0 001.414 1.414L10 12.414l3.293 3.293a1 1 0 001.414-1.414L11.414 11l3.293-3.293z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Containerization</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Docker and Kubernetes for containerized applications, microservices orchestration, and scalable deployments.
                </p>
            </div>

            <!-- Database Design -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V12a1 1 0 102 0V8.414l2.293-2.293A1 1 0 0114 6h2a1 1 0 110 2h-1.586l-2.293 2.293a1 1 0 00-.293.707V15a1 1 0 11-2 0v-3.586l-2.293-2.293A1 1 0 017 9H4a1 1 0 110-2h1.586l2.293-2.293A1 1 0 018 4.414V1a1 1 0 112 0v2.586l2.293 2.293A1 1 0 0113 6h3a1 1 0 110 2h-1.586l-2.293 2.293A1 1 0 0012 10.414V13a1 1 0 11-2 0v-1.586l-2.293-2.293A1 1 0 017 9H4a1 1 0 110-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Database Design</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    SQL and NoSQL database design including PostgreSQL, MongoDB, Redis, and Elasticsearch for optimal data storage.
                </p>
            </div>

            <!-- Monitoring & Analytics -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Monitoring & Analytics</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Implementing monitoring solutions with Prometheus, Grafana, ELK Stack, and New Relic for system observability.
                </p>
            </div>

            <!-- API Design -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">API Design</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    RESTful API design, GraphQL implementation, and API gateway solutions for seamless service communication.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Our Technology Stack -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Our <span class="text-blue-600">Technology Stack</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                We leverage cutting-edge technologies and proven frameworks to build robust, scalable systems.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Backend Technologies -->
            <div class="bg-white rounded-lg p-6 card-hover">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Backend</h3>
                <ul class="space-y-2 text-gray-600 text-sm">
                    <li>• Node.js & Express</li>
                    <li>• Python & Django/Flask</li>
                    <li>• Java & Spring Boot</li>
                    <li>• Go & Gin</li>
                    <li>• PHP & Laravel</li>
                    <li>• .NET Core</li>
                </ul>
            </div>

            <!-- Databases -->
            <div class="bg-white rounded-lg p-6 card-hover">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Databases</h3>
                <ul class="space-y-2 text-gray-600 text-sm">
                    <li>• PostgreSQL</li>
                    <li>• MySQL</li>
                    <li>• MongoDB</li>
                    <li>• Redis</li>
                    <li>• Elasticsearch</li>
                    <li>• Cassandra</li>
                </ul>
            </div>

            <!-- Cloud & DevOps -->
            <div class="bg-white rounded-lg p-6 card-hover">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Cloud & DevOps</h3>
                <ul class="space-y-2 text-gray-600 text-sm">
                    <li>• AWS / Azure / GCP</li>
                    <li>• Docker & Kubernetes</li>
                    <li>• Terraform</li>
                    <li>• Jenkins / GitLab CI</li>
                    <li>• Nginx / Apache</li>
                    <li>• CDN Solutions</li>
                </ul>
            </div>

            <!-- Message Queues -->
            <div class="bg-white rounded-lg p-6 card-hover">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Message Queues</h3>
                <ul class="space-y-2 text-gray-600 text-sm">
                    <li>• Apache Kafka</li>
                    <li>• RabbitMQ</li>
                    <li>• Amazon SQS</li>
                    <li>• Redis Pub/Sub</li>
                    <li>• Apache Pulsar</li>
                    <li>• Google Pub/Sub</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Our System Design Process -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Our <span class="text-blue-600">System Design Process</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                We follow a systematic approach to design scalable, reliable, and maintainable systems that meet your business requirements.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Step 1: Requirements Analysis -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    1
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Requirements Analysis</h3>
                <p class="text-gray-600 text-sm">
                    Understanding functional and non-functional requirements, expected load, performance criteria, and business constraints.
                </p>
            </div>

            <!-- Step 2: High-Level Design -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    2
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">High-Level Design</h3>
                <p class="text-gray-600 text-sm">
                    Creating system architecture diagrams, defining major components, and establishing communication patterns between services.
                </p>
            </div>

            <!-- Step 3: Detailed Design -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    3
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Detailed Design</h3>
                <p class="text-gray-600 text-sm">
                    Designing database schemas, API specifications, data flow diagrams, and defining scalability and security strategies.
                </p>
            </div>

            <!-- Step 4: Implementation & Testing -->
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    4
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Implementation & Testing</h3>
                <p class="text-gray-600 text-sm">
                    Building the system according to design specifications, conducting load testing, and optimizing for performance.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Our System Design Services -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Why Choose Our <span class="text-blue-600">System Design</span> Services?
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                The importance of proper system design cannot be overstated. It's the foundation that determines your application's scalability, reliability, and long-term success.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
                <h3 class="text-2xl font-bold text-gray-900 mb-6">The Importance of System Design</h3>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Scalability Planning</h4>
                            <p class="text-gray-600 text-sm">Proper system design ensures your application can handle growth from hundreds to millions of users without major rewrites.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Cost Optimization</h4>
                            <p class="text-gray-600 text-sm">Well-designed systems reduce infrastructure costs through efficient resource utilization and optimal technology choices.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Risk Mitigation</h4>
                            <p class="text-gray-600 text-sm">Identifying potential bottlenecks and failure points early prevents costly downtime and performance issues.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Faster Development</h4>
                            <p class="text-gray-600 text-sm">Clear architecture and component boundaries enable parallel development and faster feature delivery.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Our Implementation Style</h3>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Microservices Architecture</h4>
                            <p class="text-gray-600 text-sm">We design modular, loosely-coupled services that can be developed, deployed, and scaled independently.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Cloud-Native Design</h4>
                            <p class="text-gray-600 text-sm">Our systems are designed to leverage cloud services for auto-scaling, high availability, and global distribution.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Security-First Approach</h4>
                            <p class="text-gray-600 text-sm">Security is built into every layer of our system designs, from data encryption to API authentication.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-1">Observability & Monitoring</h4>
                            <p class="text-gray-600 text-sm">We implement comprehensive monitoring, logging, and alerting to ensure system health and performance visibility.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-20 bg-gradient-blue text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-6">
            Ready to Build <span class="text-blue-300">Scalable Systems?</span>
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-3xl mx-auto">
            Let's architect a system that grows with your business. Contact us today to discuss your system design requirements and build something extraordinary.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors inline-flex items-center justify-center">
                Start System Design Project
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center">
                View System Projects
            </a>
        </div>
    </div>
</section>

@endsection
