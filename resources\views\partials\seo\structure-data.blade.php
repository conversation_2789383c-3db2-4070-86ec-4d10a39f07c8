<!-- Dynamic structured data for services -->
 @push('structured_data')
@verbatim
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Service",
  "name": @json($service->name),
  "description": @json($service->description),
  "provider": {
    "@type": "Organization",
    "name": @json(__('common.company_name')),
    "url": @json(url('/'))
  },
  "serviceType": @json($service->service_type),
  "category": @json($service->category),
  "areaServed": @json($service->area_served),
  "offers": {
    "@type": "Offer",
    "availability": "https://schema.org/InStock",
    "priceCurrency": "ZAR",
    "priceRange": @json($service->price_range)
  }
}
</script>
@endverbatim
@endpush
