<?php $__env->startSection('title', $post->meta_title ?: $post->title . ' - Blog'); ?>
<?php $__env->startSection('meta_description', $post->meta_description ?: $post->excerpt); ?>
<?php $__env->startSection('meta_keywords', $post->meta_keywords); ?>

<?php $__env->startPush('head'); ?>
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta property="og:title" content="<?php echo e($post->title); ?>">
    <meta property="og:description" content="<?php echo e($post->excerpt); ?>">
    <?php if($post->featured_image): ?>
        <meta property="og:image" content="<?php echo e($post->getOptimizedImageUrl($post->featured_image, 'large')); ?>">
    <?php endif; ?>

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo e(url()->current()); ?>">
    <meta property="twitter:title" content="<?php echo e($post->title); ?>">
    <meta property="twitter:description" content="<?php echo e($post->excerpt); ?>">
    <?php if($post->featured_image): ?>
        <meta property="twitter:image" content="<?php echo e($post->getOptimizedImageUrl($post->featured_image, 'large')); ?>">
    <?php endif; ?>

    <!-- Article specific -->
    <meta property="article:published_time" content="<?php echo e($post->published_at->toISOString()); ?>">
    <meta property="article:modified_time" content="<?php echo e($post->updated_at->toISOString()); ?>">
    <meta property="article:author" content="<?php echo e($post->author->first_name); ?> <?php echo e($post->author->last_name); ?>">
    <?php if($post->category): ?>
        <meta property="article:section" content="<?php echo e($post->category->name); ?>">
    <?php endif; ?>

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": @json($post->title),
        "description": @json($post->excerpt),
        "author": {
            "@type": "Person",
            "name": @json($post->author->first_name . ' ' . $post->author->last_name)
        },
        "datePublished": @json($post->published_at->toISOString()),
        "dateModified": @json($post->updated_at->toISOString()),
        @if($post->featured_image)
        "image": @json($post->getOptimizedImageUrl($post->featured_image, 'large')),
        @endif
        "publisher": {
            "@type": "Organization",
            "name": @json(config('app.name')),
            "logo": {
                "@type": "ImageObject",
                "url": @json(asset('images/logo.png'))
            }
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "{{ url()->current() }}"
        }
    }
    </script>

<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Article Header -->
<article class="bg-white">
    <!-- Hero Section -->
    <header class="relative bg-gradient-to-r from-gray-900 to-gray-800 text-white overflow-hidden">
        <?php if($post->featured_image): ?>
            <div class="absolute inset-0 opacity-20">
                <picture>
                    <?php if($post->getWebPImageUrl($post->featured_image)): ?>
                        <source srcset="<?php echo e($post->getWebPImageUrl($post->featured_image)); ?>" type="image/webp">
                    <?php endif; ?>
                    <img src="<?php echo e($post->getOptimizedImageUrl($post->featured_image, 'large')); ?>" 
                         alt="<?php echo e($post->title); ?>" 
                         class="w-full h-full object-cover">
                </picture>
            </div>
        <?php endif; ?>
        
        <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
            <div class="max-w-4xl mx-auto text-center">
                <!-- Breadcrumb -->
                <nav class="mb-8">
                    <ol class="flex items-center justify-center space-x-2 text-sm text-gray-300">
                        <li><a href="<?php echo e(route('home')); ?>" class="hover:text-white transition-colors">Home</a></li>
                        <li><span class="mx-2">/</span></li>
                        <li><a href="<?php echo e(route('blog.index')); ?>" class="hover:text-white transition-colors">Blog</a></li>
                        <?php if($post->category): ?>
                            <li><span class="mx-2">/</span></li>
                            <li><a href="<?php echo e(route('blog.category', $post->category->slug)); ?>" class="hover:text-white transition-colors"><?php echo e($post->category->name); ?></a></li>
                        <?php endif; ?>
                    </ol>
                </nav>

                <!-- Category and Date -->
                <div class="flex items-center justify-center space-x-4 mb-6">
                    <?php if($post->category): ?>
                        <span class="px-3 py-1 bg-blue-600 text-white text-sm rounded-full"><?php echo e($post->category->name); ?></span>
                    <?php endif; ?>
                    <?php if($post->is_featured): ?>
                        <span class="px-3 py-1 bg-yellow-500 text-white text-sm rounded-full">Featured</span>
                    <?php endif; ?>
                    <time datetime="<?php echo e($post->published_at->toISOString()); ?>" class="text-gray-300">
                        <?php echo e($post->formatted_published_date); ?>

                    </time>
                </div>

                <!-- Title -->
                <h1 class="text-3xl lg:text-5xl font-bold mb-6 leading-tight">
                    <?php echo e($post->title); ?>

                </h1>

                <!-- Excerpt -->
                <?php if($post->excerpt): ?>
                    <p class="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
                        <?php echo e($post->excerpt); ?>

                    </p>
                <?php endif; ?>

                <!-- Author and Reading Time -->
                <div class="flex items-center justify-center space-x-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                            <span class="text-white font-semibold"><?php echo e(substr($post->author->first_name, 0, 1)); ?><?php echo e(substr($post->author->last_name, 0, 1)); ?></span>
                        </div>
                        <div class="text-left">
                            <div class="font-semibold"><?php echo e($post->author->first_name); ?> <?php echo e($post->author->last_name); ?></div>
                            <div class="text-sm text-gray-300">Author</div>
                        </div>
                    </div>
                    <div class="text-gray-300">
                        <div class="font-semibold"><?php echo e($post->reading_time); ?> min read</div>
                        <?php if($post->view_count > 0): ?>
                            <div class="text-sm"><?php echo e(number_format($post->view_count)); ?> views</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Wave separator -->
        <div class="absolute bottom-0 left-0 w-full">
            <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
                <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
            </svg>
        </div>
    </header>

    <!-- Article Content -->
    <div class="container mx-auto px-4 py-16">
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
                <!-- Main Content -->
                <div class="lg:col-span-3">
                    <!-- Services Tags -->
                    <?php if($post->services->count() > 0): ?>
                        <div class="mb-8">
                            <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3">Related Services</h3>
                            <div class="flex flex-wrap gap-2">
                                <?php $__currentLoopData = $post->services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="<?php echo e(route('blog.service', $service->slug)); ?>"
                                       class="px-3 py-1 bg-blue-100 text-blue-600 text-sm rounded-full hover:bg-blue-200 transition-colors">
                                        <?php echo e($service->name); ?>

                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Article Body -->
                    <div class="prose prose-lg max-w-none">
                        <?php echo $post->content; ?>

                    </div>

                    <!-- Gallery Images -->
                    <?php if($post->gallery_images && count($post->gallery_images) > 0): ?>
                        <div class="mt-12">
                            <h3 class="text-2xl font-bold text-gray-900 mb-6">Gallery</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <?php $__currentLoopData = $post->gallery_image_urls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $imageUrl): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer" 
                                         onclick="openLightbox(<?php echo e($index); ?>)">
                                        <picture>
                                            <?php if($post->getWebPImageUrl($post->gallery_images[$index])): ?>
                                                <source srcset="<?php echo e($post->getWebPImageUrl($post->gallery_images[$index])); ?>" type="image/webp">
                                            <?php endif; ?>
                                            <img src="<?php echo e($post->getOptimizedImageUrl($post->gallery_images[$index], 'medium')); ?>" 
                                                 alt="Gallery image <?php echo e($index + 1); ?>" 
                                                 class="w-full h-48 object-cover hover:scale-105 transition-transform duration-300">
                                        </picture>
                                        <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                                            <svg class="w-8 h-8 text-white opacity-0 hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Share Buttons -->
                    <div class="mt-12 pt-8 border-t border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Share this article</h3>
                        <div class="flex space-x-4">
                            <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode(url()->current())); ?>&text=<?php echo e(urlencode($post->title)); ?>" 
                               target="_blank" 
                               class="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                                <span>Twitter</span>
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(url()->current())); ?>" 
                               target="_blank" 
                               class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                                <span>Facebook</span>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e(urlencode(url()->current())); ?>" 
                               target="_blank" 
                               class="flex items-center space-x-2 px-4 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                                <span>LinkedIn</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Table of Contents (if needed) -->
                    <!-- Author Info -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">About the Author</h3>
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white font-semibold text-lg"><?php echo e(substr($post->author->first_name, 0, 1)); ?><?php echo e(substr($post->author->last_name, 0, 1)); ?></span>
                            </div>
                            <div>
                                <div class="font-semibold text-gray-900"><?php echo e($post->author->first_name); ?> <?php echo e($post->author->last_name); ?></div>
                                <div class="text-sm text-gray-600">Author</div>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600">
                            Experienced developer and writer sharing insights about modern web development and technology trends.
                        </p>
                    </div>

                    <!-- Related Posts -->
                    <?php if($relatedPosts->count() > 0): ?>
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Articles</h3>
                            <div class="space-y-4">
                                <?php $__currentLoopData = $relatedPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedPost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <article class="group">
                                        <div class="flex space-x-3">
                                            <?php if($relatedPost->featured_image): ?>
                                                <div class="flex-shrink-0">
                                                    <picture>
                                                        <?php if($relatedPost->getWebPImageUrl($relatedPost->featured_image)): ?>
                                                            <source srcset="<?php echo e($relatedPost->getWebPImageUrl($relatedPost->featured_image)); ?>" type="image/webp">
                                                        <?php endif; ?>
                                                        <img src="<?php echo e($relatedPost->getOptimizedImageUrl($relatedPost->featured_image, 'thumbnail')); ?>" 
                                                             alt="<?php echo e($relatedPost->title); ?>" 
                                                             class="w-16 h-16 object-cover rounded-lg">
                                                    </picture>
                                                </div>
                                            <?php endif; ?>
                                            <div class="flex-1 min-w-0">
                                                <h4 class="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                                                    <a href="<?php echo e(route('blog.show', $relatedPost->slug)); ?>"><?php echo e($relatedPost->title); ?></a>
                                                </h4>
                                                <p class="text-xs text-gray-500 mt-1"><?php echo e($relatedPost->formatted_published_date); ?></p>
                                            </div>
                                        </div>
                                    </article>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Comments and Rating Section -->
    <div class="container mx-auto px-4 py-16 border-t border-gray-200">
        <div class="max-w-4xl mx-auto">
            <!-- Rating Summary -->
            <?php if($post->total_ratings > 0): ?>
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-2xl font-bold text-gray-900">Reader Ratings</h3>
                        <div class="text-right">
                            <div class="flex items-center space-x-2">
                                <div class="text-3xl font-bold text-yellow-500"><?php echo e($post->formatted_average_rating); ?></div>
                                <div class="text-yellow-400 text-xl"><?php echo e($post->star_rating); ?></div>
                            </div>
                            <div class="text-sm text-gray-600"><?php echo e($post->total_ratings); ?> <?php echo e(Str::plural('rating', $post->total_ratings)); ?></div>
                        </div>
                    </div>

                    <!-- Rating Distribution -->
                    <div class="space-y-2">
                        <?php $__currentLoopData = array_reverse($post->rating_distribution, true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stars => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center space-x-1 w-16">
                                    <span class="text-sm font-medium"><?php echo e($stars); ?></span>
                                    <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-400 h-2 rounded-full" style="width: <?php echo e($post->total_ratings > 0 ? ($count / $post->total_ratings) * 100 : 0); ?>%"></div>
                                </div>
                                <div class="text-sm text-gray-600 w-12"><?php echo e($count); ?></div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Comment Form -->
            <?php if(auth()->guard()->check()): ?>
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Leave a Comment</h3>

                    <form id="comment-form" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="parent_comment_id" id="parent-comment-id" value="">

                        <!-- Rating Section -->
                        <?php if(!$post->hasUserRated(Auth::id())): ?>
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Rate this article (optional)</label>
                                <div class="flex items-center space-x-1" id="rating-stars">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <button type="button"
                                                class="rating-star text-2xl text-gray-300 hover:text-yellow-400 transition-colors"
                                                data-rating="<?php echo e($i); ?>">
                                            ★
                                        </button>
                                    <?php endfor; ?>
                                </div>
                                <input type="hidden" name="rating" id="rating-input" value="">
                                <p class="text-xs text-gray-500 mt-1">Click on stars to rate this article</p>
                            </div>
                        <?php else: ?>
                            <div class="mb-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    <span class="text-sm text-yellow-800">You rated this article <?php echo e($post->getUserRating(Auth::id())); ?> stars</span>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Comment Content -->
                        <div class="mb-6">
                            <label for="comment-content" class="block text-sm font-medium text-gray-700 mb-2">Your Comment</label>
                            <textarea id="comment-content"
                                      name="content"
                                      rows="4"
                                      class="form-textarea w-full border-gray-300 rounded-lg focus:border-blue-500 focus:ring-blue-500"
                                      placeholder="Share your thoughts about this article..."
                                      required
                                      minlength="10"
                                      maxlength="2000"></textarea>
                            <p class="text-xs text-gray-500 mt-1">Minimum 10 characters, maximum 2000 characters</p>
                        </div>

                        <!-- File Attachments -->
                        <div class="mb-6">
                            <label for="comment-attachments" class="block text-sm font-medium text-gray-700 mb-2">Attachments (optional)</label>
                            <input type="file"
                                   id="comment-attachments"
                                   name="attachments[]"
                                   multiple
                                   accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt"
                                   class="form-input w-full border-gray-300 rounded-lg focus:border-blue-500 focus:ring-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Max 3 files, 10MB each. Supported: Images, PDFs, Documents</p>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex items-center justify-between">
                            <div id="reply-info" class="hidden">
                                <span class="text-sm text-gray-600">Replying to <span id="reply-to-name" class="font-medium"></span></span>
                                <button type="button" id="cancel-reply" class="ml-2 text-sm text-blue-600 hover:text-blue-800">Cancel</button>
                            </div>
                            <div class="flex space-x-3">
                                <button type="button" id="cancel-comment" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                                    Cancel
                                </button>
                                <button type="submit" id="submit-comment" class="btn-primary">
                                    <span class="submit-text">Submit Comment</span>
                                    <span class="loading-text hidden">Submitting...</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            <?php else: ?>
                <div class="bg-gray-50 rounded-lg p-6 mb-8 text-center">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Join the Discussion</h3>
                    <p class="text-gray-600 mb-4">Please log in to leave a comment and rate this article.</p>
                    <div class="flex justify-center space-x-4">
                        <a href="<?php echo e(route('login')); ?>" class="btn-primary">Login</a>
                        <a href="<?php echo e(route('register')); ?>" class="btn-secondary">Register</a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Comments Display -->
            <?php if($post->comment_count > 0): ?>
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">
                        Comments (<?php echo e($post->comment_count); ?>)
                    </h3>

                    <div class="space-y-6" id="comments-container">
                        <?php $__currentLoopData = $post->topLevelComments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo $__env->make('partials.comment', ['comment' => $comment, 'level' => 0], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="bg-gray-50 rounded-lg p-6 text-center">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">No Comments Yet</h3>
                    <p class="text-gray-600">Be the first to share your thoughts about this article!</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</article>

<!-- Image Lightbox Modal -->
<?php if($post->gallery_images && count($post->gallery_images) > 0): ?>
    <div id="lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center">
        <div class="relative max-w-4xl max-h-full p-4">
            <button onclick="closeLightbox()" class="absolute top-4 right-4 text-white hover:text-gray-300 z-10">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full object-contain">
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                <button onclick="previousImage()" class="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button onclick="nextImage()" class="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<?php if($post->gallery_images && count($post->gallery_images) > 0): ?>
<script>
const galleryImages = <?php echo json_encode($post->gallery_image_urls, 15, 512) ?>;
let currentImageIndex = 0;

document.addEventListener('DOMContentLoaded', function() {
    // Lightbox functionality
    function openLightbox(index) {
        currentImageIndex = index;
        document.getElementById('lightbox-image').src = galleryImages[index];
        document.getElementById('lightbox').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function closeLightbox() {
        document.getElementById('lightbox').classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    function nextImage() {
        currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
        document.getElementById('lightbox-image').src = galleryImages[currentImageIndex];
    }

    function previousImage() {
        currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
        document.getElementById('lightbox-image').src = galleryImages[currentImageIndex];
    }

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (!document.getElementById('lightbox').classList.contains('hidden')) {
            if (e.key === 'Escape') closeLightbox();
            if (e.key === 'ArrowRight') nextImage();
            if (e.key === 'ArrowLeft') previousImage();
        }
    });

    // Image modal for comment attachments
    document.addEventListener('click', function(e) {
        if (e.target.id === 'modal-image') {
            closeImageModal();
        }

        // Reply functionality
        if (e.target.classList.contains('reply-btn')) {
            const commentId = e.target.dataset.commentId;
            const userName = e.target.dataset.userName;
            setupReply(commentId, userName);
        }

        if (e.target.id === 'cancel-reply') {
            cancelReply();
        }

        if (e.target.classList.contains('helpful-btn')) {
            toggleHelpful(e.target);
        }

        if (e.target.classList.contains('flag-btn')) {
            flagComment(e.target);
        }
    });

    // Rating stars functionality
    const ratingStars = document.querySelectorAll('.rating-star');
    const ratingInput = document.getElementById('rating-input');

    ratingStars.forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            ratingInput.value = rating;

            // Update star display
            ratingStars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.remove('text-gray-300');
                    s.classList.add('text-yellow-400');
                } else {
                    s.classList.remove('text-yellow-400');
                    s.classList.add('text-gray-300');
                }
            });
        });

        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.dataset.rating);
            ratingStars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('text-yellow-300');
                } else {
                    s.classList.remove('text-yellow-300');
                }
            });
        });
    });

    // Comment form submission
    const commentForm = document.getElementById('comment-form');
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitComment();
        });
    }
});

// Comment system functions
function submitComment() {
    const form = document.getElementById('comment-form');
    const submitBtn = document.getElementById('submit-comment');
    const submitText = submitBtn.querySelector('.submit-text');
    const loadingText = submitBtn.querySelector('.loading-text');

    // Show loading state
    submitBtn.disabled = true;
    submitText.classList.add('hidden');
    loadingText.classList.remove('hidden');

    const formData = new FormData(form);

    fetch('<?php echo e(route("comments.store", $post->slug)); ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            form.reset();
            document.getElementById('rating-input').value = '';
            resetRatingStars();
            cancelReply();

            // Optionally reload comments section
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while submitting your comment.', 'error');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingText.classList.add('hidden');
    });
}

function setupReply(commentId, userName) {
    document.getElementById('parent-comment-id').value = commentId;
    document.getElementById('reply-to-name').textContent = userName;
    document.getElementById('reply-info').classList.remove('hidden');
    document.getElementById('comment-content').focus();
    document.getElementById('comment-content').placeholder = `Reply to ${userName}...`;
}

function cancelReply() {
    document.getElementById('parent-comment-id').value = '';
    document.getElementById('reply-info').classList.add('hidden');
    document.getElementById('comment-content').placeholder = 'Share your thoughts about this article...';
}

function resetRatingStars() {
    const ratingStars = document.querySelectorAll('.rating-star');
    ratingStars.forEach(star => {
        star.classList.remove('text-yellow-400');
        star.classList.add('text-gray-300');
    });
}

function toggleHelpful(button) {
    const commentId = button.dataset.commentId;
    const isHelpful = button.dataset.isHelpful === 'true';
    const method = isHelpful ? 'DELETE' : 'POST';
    const url = isHelpful ?
        `/comments/${commentId}/helpful` :
        `/comments/${commentId}/helpful`;

    fetch(url, {
        method: method,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.dataset.isHelpful = isHelpful ? 'false' : 'true';
            button.querySelector('.helpful-count').textContent = data.helpful_count;

            if (isHelpful) {
                button.classList.remove('text-blue-600');
                button.classList.add('text-gray-500');
            } else {
                button.classList.remove('text-gray-500');
                button.classList.add('text-blue-600');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred.', 'error');
    });
}

function flagComment(button) {
    const commentId = button.dataset.commentId;

    if (confirm('Are you sure you want to flag this comment as inappropriate?')) {
        fetch(`/comments/${commentId}/flag`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Server responded with an error: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            showNotification(data.message, data.success ? 'success' : 'error');
            if (data.success) {
                button.disabled = true;
                button.textContent = 'Flagged';
                button.classList.add('text-red-600');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred: ' + error.message, 'error');
        });
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function openImageModal(src) {
    document.getElementById('modal-image').src = src;
    document.getElementById('image-modal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    document.getElementById('image-modal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}
</script>
<?php endif; ?>

<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/pages/blog-post.blade.php ENDPATH**/ ?>