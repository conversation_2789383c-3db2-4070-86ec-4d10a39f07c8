<?php $__env->startSection('title', 'Start Your Project - Professional Digital Services Application - ChiSolution'); ?>
<?php $__env->startSection('meta_description', 'Apply for professional digital services including web development, mobile apps, e-commerce solutions, and digital marketing.'); ?>
<?php $__env->startSection('meta_keywords', 'digital services, web development, mobile apps, e-commerce, digital marketing, South Africa'); ?>

<!-- <?php $__env->startPush('structured_data'); ?> -->
<!-- <script type="application/ld+json">
{

  "@type": "Service",
  "name": <?php echo json_encode(__('apply.service_name'), 15, 512) ?>,
  "description": <?php echo json_encode(__('apply.service_description'), 15, 512) ?>,
  "provider": {
    "@type": "Organization",
    "name": <?php echo json_encode(__('common.company_name'), 15, 512) ?>,
    "url": <?php echo json_encode(url('/'), 15, 512) ?>,
    "logo": <?php echo json_encode(asset('images/logo.png'), 15, 512) ?>,
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+27-11-123-4567",
      "contactType": "customer service",
      "areaServed": "ZA",
      "availableLanguage": ["English", "Afrikaans"]
    }
  },
  "serviceType": "Digital Services",
  "areaServed": {
    "@type": "Country",
    "name": "South Africa"
  },
  "potentialAction": {
    "@type": "ApplyAction",
    "target": <?php echo json_encode(url()->current(), 15, 512) ?>,
    "name": "Apply for Digital Services"
  }
}
</script> -->


<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4"><?php echo e(__('apply.page_title')); ?></h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
                <?php echo e(__('apply.page_description')); ?>

            </p>
            <div class="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <?php echo e(__('apply.benefit_consultation')); ?>

                </span>
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <?php echo e(__('apply.benefit_response')); ?>

                </span>
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <?php echo e(__('apply.benefit_solutions')); ?>

                </span>
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <?php echo e(__('apply.benefit_team')); ?>

                </span>
            </div>
        </div>

        <!-- Services Overview -->
        <?php if($services->count() > 0): ?>
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 text-center mb-8"><?php echo e(__('apply.services_title')); ?></h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__currentLoopData = $services->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow">
                    <div class="flex items-center mb-4">
                        <?php if($service->icon): ?>
                            <i class="<?php echo e($service->icon); ?> text-2xl text-blue-600 mr-3"></i>
                        <?php else: ?>
                            <svg class="w-8 h-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        <?php endif; ?>
                        <h3 class="text-lg font-semibold text-gray-900"><?php echo e($service->name); ?></h3>
                    </div>
                    <p class="text-gray-600 text-sm"><?php echo e($service->short_description ?? __('apply.service_default_description', ['service' => $service->name])); ?></p>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php if($services->count() > 6): ?>
                <div class="text-center mt-8">
                    <a href="<?php echo e(route('services.index')); ?>" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                        <?php echo e(__('apply.view_all_services', ['count' => $services->count()])); ?>

                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Application Form -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="px-6 py-8">
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2"><?php echo e(__('apply.form_title')); ?></h2>
                    <p class="text-gray-600"><?php echo e(__('apply.form_description')); ?></p>
                </div>

                <form action="<?php echo e(route('apply.project.store')); ?>" method="POST" enctype="multipart/form-data" class="space-y-8" itemscope itemtype="https://schema.org/ContactForm">
                    <?php echo csrf_field(); ?>

                    <!-- Service Selection -->
                    <div class="floating-input-group">
                        <label for="service_id" class="block text-sm font-medium text-gray-700 mb-2"><?php echo e(__('apply.service_label')); ?></label>
                        <select id="service_id" name="service_id" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" aria-describedby="service_id_help">
                            <option value=""><?php echo e(__('apply.service_placeholder')); ?></option>
                            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($service->id); ?>" <?php echo e(old('service_id') == $service->id ? 'selected' : ''); ?>>
                                    <?php echo e($service->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <p id="service_id_help" class="mt-1 text-sm text-gray-500"><?php echo e(__('apply.service_help')); ?></p>
                        <?php $__errorArgs = ['service_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600" role="alert"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Project Title -->
                    <div class="floating-input-group">
                        <input type="text" id="title" name="title" value="<?php echo e(old('title')); ?>" required
                               class="floating-input peer" placeholder=" ">
                        <label for="title" class="floating-label"><?php echo e(__('apply.project_title')); ?> *</label>
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Project Description -->
                    <div class="floating-input-group">
                        <textarea id="description" name="description" rows="6" required
                                  class="floating-input peer resize-vertical" placeholder=" "><?php echo e(old('description')); ?></textarea>
                        <label for="description" class="floating-label"><?php echo e(__('apply.project_description')); ?> *</label>
                        <p class="mt-1 text-sm text-gray-500"><?php echo e(__('apply.project_description_help')); ?></p>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Requirements -->
                    <div class="floating-input-group">
                        <textarea id="requirements" name="requirements" rows="4"
                                  class="floating-input peer resize-vertical" placeholder=" "><?php echo e(old('requirements')); ?></textarea>
                        <label for="requirements" class="floating-label"><?php echo e(__('apply.technical_requirements')); ?></label>
                        <p class="mt-1 text-sm text-gray-500"><?php echo e(__('apply.technical_requirements_help')); ?></p>
                        <?php $__errorArgs = ['requirements'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Budget and Timeline Row -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="floating-input-group">
                            <select id="budget_range" name="budget_range" class="floating-input peer">
                                <option value=""></option>
                                <option value="under-5k" <?php echo e(old('budget_range') == 'under-5k' ? 'selected' : ''); ?>><?php echo e(__('apply.budget_under_5k')); ?></option>
                                <option value="5k-15k" <?php echo e(old('budget_range') == '5k-15k' ? 'selected' : ''); ?>><?php echo e(__('apply.budget_5k_15k')); ?></option>
                                <option value="15k-50k" <?php echo e(old('budget_range') == '15k-50k' ? 'selected' : ''); ?>><?php echo e(__('apply.budget_15k_50k')); ?></option>
                                <option value="50k-100k" <?php echo e(old('budget_range') == '50k-100k' ? 'selected' : ''); ?>><?php echo e(__('apply.budget_50k_100k')); ?></option>
                                <option value="over-100k" <?php echo e(old('budget_range') == 'over-100k' ? 'selected' : ''); ?>><?php echo e(__('apply.budget_over_100k')); ?></option>
                                <option value="discuss" <?php echo e(old('budget_range') == 'discuss' ? 'selected' : ''); ?>><?php echo e(__('apply.budget_discuss')); ?></option>
                            </select>
                            <label for="budget_range" class="floating-label"><?php echo e(__('apply.budget_range')); ?></label>
                            <?php $__errorArgs = ['budget_range'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="floating-input-group">
                            <select id="timeline" name="timeline" class="floating-input peer">
                                <option value=""></option>
                                <option value="asap" <?php echo e(old('timeline') == 'asap' ? 'selected' : ''); ?>><?php echo e(__('apply.timeline_asap')); ?></option>
                                <option value="1-month" <?php echo e(old('timeline') == '1-month' ? 'selected' : ''); ?>><?php echo e(__('apply.timeline_1_month')); ?></option>
                                <option value="2-3-months" <?php echo e(old('timeline') == '2-3-months' ? 'selected' : ''); ?>><?php echo e(__('apply.timeline_2_3_months')); ?></option>
                                <option value="3-6-months" <?php echo e(old('timeline') == '3-6-months' ? 'selected' : ''); ?>><?php echo e(__('apply.timeline_3_6_months')); ?></option>
                                <option value="6-months-plus" <?php echo e(old('timeline') == '6-months-plus' ? 'selected' : ''); ?>><?php echo e(__('apply.timeline_6_months_plus')); ?></option>
                                <option value="flexible" <?php echo e(old('timeline') == 'flexible' ? 'selected' : ''); ?>><?php echo e(__('apply.timeline_flexible')); ?></option>
                            </select>
                            <label for="timeline" class="floating-label"><?php echo e(__('apply.timeline')); ?></label>
                            <?php $__errorArgs = ['timeline'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Priority -->
                    <div class="floating-input-group">
                        <select id="priority" name="priority" required class="floating-input peer">
                            <option value=""></option>
                            <option value="low" <?php echo e(old('priority') == 'low' ? 'selected' : ''); ?>><?php echo e(__('apply.priority_low')); ?></option>
                            <option value="medium" <?php echo e(old('priority', 'medium') == 'medium' ? 'selected' : ''); ?>><?php echo e(__('apply.priority_medium')); ?></option>
                            <option value="high" <?php echo e(old('priority') == 'high' ? 'selected' : ''); ?>><?php echo e(__('apply.priority_high')); ?></option>
                            <option value="urgent" <?php echo e(old('priority') == 'urgent' ? 'selected' : ''); ?>><?php echo e(__('apply.priority_urgent')); ?></option>
                        </select>
                        <label for="priority" class="floating-label"><?php echo e(__('apply.priority')); ?> *</label>
                        <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- File Upload Section -->
                    <div class="space-y-4">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-900"><?php echo e(__('apply.attachments_title')); ?></h3>
                            <span class="text-sm text-gray-500">(<?php echo e(__('apply.optional')); ?>)</span>
                        </div>

                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors duration-200">
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="mt-4">
                                    <label for="attachments" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">
                                            <?php echo e(__('apply.upload_description')); ?>

                                        </span>
                                        <span class="mt-1 block text-xs text-gray-500">
                                            <?php echo e(__('apply.upload_formats')); ?>

                                        </span>
                                    </label>
                                    <input id="attachments" name="attachments[]" type="file" class="sr-only" multiple
                                           accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.zip,.jpg,.jpeg,.png,.webp">
                                </div>
                                <!-- Security Information -->
                                <div class="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <div class="text-sm text-green-800">
                                            <p class="font-medium mb-1"><?php echo e(__('apply.security_title')); ?></p>
                                            <ul class="list-disc list-inside space-y-1 text-green-700">
                                                <li><?php echo e(__('apply.security_scanning')); ?></li>
                                                <li><?php echo e(__('apply.security_analysis')); ?></li>
                                                <li><?php echo e(__('apply.security_encryption')); ?></li>
                                                <li><?php echo e(__('apply.security_validation')); ?></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- File Preview Area -->
                        <div id="file-preview" class="hidden space-y-2">
                            <h4 class="text-sm font-medium text-gray-900"><?php echo e(__('apply.selected_files')); ?>:</h4>
                            <div id="file-list" class="space-y-2"></div>
                        </div>

                        <?php $__errorArgs = ['attachments'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <?php $__errorArgs = ['attachments.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4">
                        <a href="<?php echo e(route('contact', ['locale' => app()->getLocale()])); ?>"
                           class="btn-outline border-gray-300 text-gray-700 hover:bg-gray-50">
                            <?php echo e(__('apply.cancel')); ?>

                        </a>
                        <button type="submit" class="btn-primary">
                            <?php echo e(__('apply.submit_application')); ?>

                            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- SEO Content Section -->
        <div class="mt-16 bg-white rounded-lg shadow-lg p-8">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center"><?php echo e(__('apply.why_choose_title')); ?></h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('apply.benefit_fast_delivery')); ?></h3>
                        <p class="text-gray-600"><?php echo e(__('apply.benefit_fast_delivery_desc')); ?></p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('apply.benefit_quality')); ?></h3>
                        <p class="text-gray-600"><?php echo e(__('apply.benefit_quality_desc')); ?></p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('apply.benefit_expert_team')); ?></h3>
                        <p class="text-gray-600"><?php echo e(__('apply.benefit_expert_team_desc')); ?></p>
                    </div>
                </div>

                <div class="prose max-w-none">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4"><?php echo e(__('apply.services_overview_title')); ?></h3>
                    <p class="text-gray-600 mb-4">
                        <?php echo e(__('apply.services_overview_description')); ?>

                    </p>

                    <h4 class="text-xl font-semibold text-gray-900 mb-3"><?php echo e(__('apply.service_areas_title')); ?></h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e(__('apply.service_web_development')); ?>

                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e(__('apply.service_mobile_development')); ?>

                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e(__('apply.service_ecommerce')); ?>

                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e(__('apply.service_digital_marketing')); ?>

                            </li>
                        </ul>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e(__('apply.service_seo')); ?>

                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e(__('apply.service_ui_ux')); ?>

                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e(__('apply.service_maintenance')); ?>

                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e(__('apply.service_custom_software')); ?>

                            </li>
                        </ul>
                    </div>

                    <p class="text-gray-600">
                        <?php echo e(__('apply.closing_message')); ?>

                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // File Upload Handling (reuse the same logic from contact form)
    const fileInput = document.getElementById('attachments');
    const filePreview = document.getElementById('file-preview');
    const fileList = document.getElementById('file-list');
    const maxFileSize = 25 * 1024 * 1024; // 25MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'application/zip',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp'
    ];

    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            fileList.innerHTML = '';
            
            if (files.length === 0) {
                filePreview.classList.add('hidden');
                return;
            }

            files.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg';
                
                // Validate file
                let isValid = true;
                let errorMessage = '';
                
                if (file.size > maxFileSize) {
                    isValid = false;
                    errorMessage = 'File too large (max 25MB)';
                } else if (!allowedTypes.includes(file.type)) {
                    isValid = false;
                    errorMessage = 'File type not allowed';
                }
                
                const fileInfo = document.createElement('div');
                fileInfo.className = 'flex items-center space-x-3';
                
                const fileIcon = getFileIcon(file.type);
                const fileSize = formatFileSize(file.size);
                
                fileInfo.innerHTML = `
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg ${isValid ? 'bg-blue-100' : 'bg-red-100'}">
                        ${fileIcon}
                    </div>
                    <div>
                        <p class="text-sm font-medium ${isValid ? 'text-gray-900' : 'text-red-900'}">${file.name}</p>
                        <p class="text-xs ${isValid ? 'text-gray-500' : 'text-red-500'}">
                            ${isValid ? fileSize : errorMessage}
                        </p>
                    </div>
                `;
                
                const removeButton = document.createElement('button');
                removeButton.type = 'button';
                removeButton.className = 'text-gray-400 hover:text-red-500 transition-colors duration-200';
                removeButton.innerHTML = `
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                `;
                
                removeButton.addEventListener('click', function() {
                    fileItem.remove();
                    if (fileList.children.length === 0) {
                        filePreview.classList.add('hidden');
                    }
                });
                
                fileItem.appendChild(fileInfo);
                fileItem.appendChild(removeButton);
                fileList.appendChild(fileItem);
            });
            
            filePreview.classList.remove('hidden');
        });
    }

    function getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) {
            return '<svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType === 'application/pdf') {
            return '<svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        } else {
            return '<svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/project-applications/create.blade.php ENDPATH**/ ?>