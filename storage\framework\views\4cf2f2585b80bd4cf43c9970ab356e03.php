<?php $__env->startSection('title', 'User Permissions - ' . $user->first_name . ' ' . $user->last_name); ?>

<?php
use Illuminate\Support\Facades\Storage;
?>

<?php $__env->startSection('content'); ?>
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">User Permissions</h1>
            <p class="text-gray-600"><?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?> (<?php echo e($user->email); ?>)</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('admin.permissions.index')); ?>" 
               class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Users
            </a>
            <a href="<?php echo e(route('admin.users.edit', $user)); ?>" 
               class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Edit User
            </a>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <div class="flex items-center space-x-6">
            <div class="flex-shrink-0">
                <?php if($user->avatar): ?>
                    <img class="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
                         src="<?php echo e(Storage::url($user->avatar)); ?>"
                         alt="<?php echo e($user->full_name); ?>">
                <?php else: ?>
                    <div class="h-20 w-20 rounded-full bg-primary-100 flex items-center justify-center border-2 border-gray-200">
                        <span class="text-2xl font-medium text-primary-600">
                            <?php echo e(strtoupper(substr($user->first_name, 0, 1) . substr($user->last_name, 0, 1))); ?>

                        </span>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="flex-1">
                <h2 class="text-xl font-semibold text-gray-900"><?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?></h2>
                <p class="text-gray-600"><?php echo e($user->email); ?></p>
                
                <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Current Role</span>
                        <div class="mt-1">
                            <select onchange="updateUserRole('<?php echo e($user->uuid); ?>', this.value)" 
                                    class="text-sm border border-neutral-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <?php $__currentLoopData = \App\Models\Role::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($role->id); ?>" <?php echo e($user->role_id === $role->id ? 'selected' : ''); ?>>
                                        <?php echo e(ucfirst($role->name)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Status</span>
                        <div class="mt-1">
                            <?php if($user->is_active): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    Active
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                    </svg>
                                    Inactive
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-500">Member Since</span>
                        <div class="mt-1 text-sm text-gray-900">
                            <?php echo e($user->created_at->format('M j, Y')); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Permissions -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <div class="p-6 border-b border-neutral-200">
            <h2 class="text-lg font-semibold text-gray-900">Current Permissions</h2>
            <p class="text-gray-600">Permissions granted through the <?php echo e($user->role ? ucfirst($user->role->name) : 'No Role'); ?> role</p>
        </div>

        <div class="p-6">
            <?php if($user->role && !empty($userPermissions)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $userPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource => $actions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-neutral-200 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-3">
                                <?php echo e(ucfirst(str_replace('_', ' ', $resource))); ?>

                            </h4>
                            
                            <div class="space-y-2">
                                <?php $__currentLoopData = $actions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        <span class="text-sm text-gray-700"><?php echo e(ucfirst($action)); ?></span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No Permissions</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        <?php echo e($user->role ? 'This role has no permissions assigned.' : 'This user has no role assigned.'); ?>

                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Permission Comparison -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <div class="p-6 border-b border-neutral-200">
            <h2 class="text-lg font-semibold text-gray-900">Permission Comparison</h2>
            <p class="text-gray-600">Compare permissions across all available roles</p>
        </div>

        <div class="p-6 overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-neutral-200">
                        <th class="text-left py-3 px-4 font-medium text-gray-900">Resource</th>
                        <?php $__currentLoopData = \App\Models\Role::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <th class="text-center py-3 px-4 font-medium text-gray-900 <?php echo e($user->role_id === $role->id ? 'bg-primary-50' : ''); ?>">
                                <?php echo e(ucfirst($role->name)); ?>

                                <?php if($user->role_id === $role->id): ?>
                                    <span class="block text-xs text-primary-600 font-normal">(Current)</span>
                                <?php endif; ?>
                            </th>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $availablePermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource => $actions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="border-b border-neutral-100 hover:bg-neutral-50">
                            <td class="py-3 px-4 font-medium text-gray-900">
                                <?php echo e(ucfirst(str_replace('_', ' ', $resource))); ?>

                            </td>
                            <?php $__currentLoopData = \App\Models\Role::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="py-3 px-4 text-center <?php echo e($user->role_id === $role->id ? 'bg-primary-50' : ''); ?>">
                                    <?php
                                        $rolePermissions = $role->permissions[$resource] ?? [];
                                        $hasAnyPermission = !empty($rolePermissions);
                                    ?>
                                    
                                    <?php if($hasAnyPermission): ?>
                                        <div class="flex flex-wrap justify-center gap-1">
                                            <?php $__currentLoopData = $rolePermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                                    <?php echo e(ucfirst($action)); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-gray-400">—</span>
                                    <?php endif; ?>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
async function updateUserRole(userId, roleId) {
    try {
        const response = await fetch(`/admin/permissions/users/${userId}/role`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ role_id: roleId })
        });

        const data = await response.json();
        
        if (data.success) {
            showNotification('User role updated successfully', 'success');
            // Reload page to show updated permissions
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Failed to update user role', 'error');
        }
    } catch (error) {
        showNotification('An error occurred while updating user role', 'error');
    }
}

function showNotification(message, type) {
    // Implementation depends on your notification system
    // This is a simple alert for now
    alert(message);
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/admin/permissions/show.blade.php ENDPATH**/ ?>