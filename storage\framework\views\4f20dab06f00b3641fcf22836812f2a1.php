<?php $__env->startSection('title', 'Contact Submission Details - Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-start">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Contact Submission Details</h1>
            <p class="text-gray-600 mt-1">Submitted <?php echo e($contactSubmission->created_at->format('M j, Y \a\t g:i A')); ?></p>
        </div>
        <div class="flex items-center space-x-2">
            <?php if(!$contactSubmission->is_read): ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    New
                </span>
            <?php endif; ?>
            <?php if($contactSubmission->is_spam): ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Spam
                </span>
            <?php endif; ?>
            <?php if($contactSubmission->replied_at): ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Replied
                </span>
            <?php endif; ?>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            Contact Information
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                    <dd class="mt-1 text-sm text-gray-900"><?php echo e($contactSubmission->name); ?></dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <a href="mailto:<?php echo e($contactSubmission->email); ?>" class="text-blue-600 hover:text-blue-800">
                            <?php echo e($contactSubmission->email); ?>

                        </a>
                    </dd>
                </div>
                <?php if($contactSubmission->phone): ?>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <a href="tel:<?php echo e($contactSubmission->phone); ?>" class="text-blue-600 hover:text-blue-800">
                            <?php echo e($contactSubmission->phone); ?>

                        </a>
                    </dd>
                </div>
                <?php endif; ?>
            </div>
            <div class="space-y-4">
                <?php if($contactSubmission->company): ?>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Company</dt>
                    <dd class="mt-1 text-sm text-gray-900"><?php echo e($contactSubmission->company); ?></dd>
                </div>
                <?php endif; ?>
                <?php if($contactSubmission->service): ?>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Service Interest</dt>
                    <dd class="mt-1">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <?php echo e(ucfirst($contactSubmission->service)); ?>

                        </span>
                    </dd>
                </div>
                <?php endif; ?>
                <?php if($contactSubmission->subject): ?>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Subject</dt>
                    <dd class="mt-1 text-sm text-gray-900"><?php echo e($contactSubmission->subject); ?></dd>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Message -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            Message
        </h2>
        <div class="bg-gray-50 rounded-lg p-4">
            <p class="text-gray-900 whitespace-pre-wrap leading-relaxed"><?php echo e($contactSubmission->message); ?></p>
        </div>
    </div>

    <!-- Attachments -->
    <?php if($contactSubmission->attachments && count($contactSubmission->attachments) > 0): ?>
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
            </svg>
            Attachments (<?php echo e(count($contactSubmission->attachments)); ?>)
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <?php $__currentLoopData = $contactSubmission->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <div class="flex-shrink-0">
                    <?php if(str_starts_with($attachment['mime_type'] ?? '', 'image/')): ?>
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    <?php else: ?>
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    <?php endif; ?>
                </div>
                <div class="ml-3 flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">
                        <?php echo e($attachment['original_name'] ?? 'Unknown file'); ?>

                    </p>
                    <p class="text-sm text-gray-500">
                        <?php echo e(number_format(($attachment['size'] ?? 0) / 1024, 1)); ?> KB
                        <?php if(isset($attachment['mime_type'])): ?>
                            • <?php echo e($attachment['mime_type']); ?>

                        <?php endif; ?>
                    </p>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Technical Information -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Technical Information
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Submission ID</dt>
                    <dd class="mt-1 text-sm text-gray-900 font-mono"><?php echo e($contactSubmission->uuid); ?></dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">IP Address</dt>
                    <dd class="mt-1 text-sm text-gray-900"><?php echo e($contactSubmission->ip_address ?: 'Not recorded'); ?></dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="mt-1">
                        <?php if($contactSubmission->is_spam): ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Marked as Spam
                            </span>
                        <?php elseif($contactSubmission->is_read): ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Read
                            </span>
                        <?php else: ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                Unread
                            </span>
                        <?php endif; ?>
                    </dd>
                </div>
            </div>
            <div class="space-y-4">
                <?php if($contactSubmission->referrer): ?>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Referrer</dt>
                    <dd class="mt-1 text-sm text-gray-900 break-all"><?php echo e($contactSubmission->referrer); ?></dd>
                </div>
                <?php endif; ?>
                <?php if($contactSubmission->replied_at): ?>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Replied At</dt>
                    <dd class="mt-1 text-sm text-gray-900"><?php echo e($contactSubmission->replied_at->format('M j, Y g:i A')); ?></dd>
                </div>
                <?php endif; ?>
                <?php if($contactSubmission->repliedBy): ?>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Replied By</dt>
                    <dd class="mt-1 text-sm text-gray-900"><?php echo e($contactSubmission->repliedBy->name); ?></dd>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <h2 class="text-xl font-bold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            Quick Actions
        </h2>
        <div class="flex flex-wrap gap-3">
            <a href="mailto:<?php echo e($contactSubmission->email); ?>?subject=Re: <?php echo e($contactSubmission->subject ?: 'Your inquiry'); ?>" 
               class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Reply via Email
            </a>

            <?php if(!$contactSubmission->is_spam): ?>
                <button onclick="toggleSpam('<?php echo e($contactSubmission->uuid); ?>', false)" 
                        class="btn btn-lg btn-danger">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                    </svg>
                    Mark as Spam
                </button>
            <?php else: ?>
                <button onclick="toggleSpam('<?php echo e($contactSubmission->uuid); ?>', true)" 
                        class="btn btn-lg btn-success">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Unmark as Spam
                </button>
            <?php endif; ?>

            <a href="<?php echo e(route('admin.contact-submissions.index')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to List
            </a>

            <form method="POST" action="<?php echo e(route('admin.contact-submissions.destroy', $contactSubmission)); ?>" 
                  class="inline" onsubmit="return confirm('Are you sure you want to delete this submission? This action cannot be undone.')">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <button type="submit" class="btn btn-lg btn-danger">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Toggle spam status
function toggleSpam(uuid, currentStatus) {
    fetch(`/admin/contact-submissions/${uuid}/mark-spam`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            setTimeout(() => location.reload(), 1000); // Refresh to update UI
        } else {
            showToast('Failed to update spam status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

// Toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    toast.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Slide in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Slide out and remove
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/admin/contact-submissions/show.blade.php ENDPATH**/ ?>