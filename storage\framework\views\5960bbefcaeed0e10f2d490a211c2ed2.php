<?php $__env->startSection('title', __('common.blog') . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', 'Stay updated with the latest insights, tutorials, and industry trends in web development, mobile apps, and digital marketing.'); ?>
<?php $__env->startSection('meta_keywords', 'blog, web development, mobile apps, digital marketing, tutorials, industry insights, technology trends'); ?>

<?php $__env->startPush('structured_data'); ?>

<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": @json('Blog'),
        "description": @json('Stay updated with the latest insights, tutorials, and industry trends in web development, mobile apps, and digital marketing.'),
        "url": @json(url()->current()),
        "mainEntity": {
            "@type": "ItemList",
            "numberOfItems": @json($posts->total()),
            "itemListElement": [
                @foreach($posts as $index => $post)
                {
                    "@type": "ListItem",
                    "position": {{ $index + 1 }},
                    "item": {
                        "@type": "BlogPosting",
                        "name": @json($post->title),
                        "description": @json($post->meta_description ?: Str::limit(strip_tags($post->content), 160)),
                        "url": @json(route('blog.show', ['post' => $post->slug])),
                        "datePublished": @json($post->published_at->toISOString()),
                        "dateModified": @json($post->updated_at->toISOString())
                    }
                }
                @if(!$loop->last),@endif
                @endforeach
            ]
        }
    }
</script>

<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="heading-1 text-white mb-6">
                Our <span class="text-blue-300">Blog</span>
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                Insights, tutorials, and industry trends to help you stay ahead in the digital world. Learn from our experience and expertise.
            </p>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Featured Post -->
<?php if($posts->isNotEmpty()): ?>
    <?php
        $featuredPost = $posts->where('is_featured', true)->first() ?? $posts->first();
    ?>
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="heading-2 mb-4">
                    Featured <span class="text-blue-600">Article</span>
                </h2>
            </div>

            <div class="max-w-4xl mx-auto">
                <article class="card-hover">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                        <div class="relative overflow-hidden rounded-lg">
                            <?php if($featuredPost->featured_image): ?>
                                <picture>
                                    <?php if($featuredPost->getWebPImageUrl($featuredPost->featured_image)): ?>
                                        <source srcset="<?php echo e($featuredPost->getWebPImageUrl($featuredPost->featured_image)); ?>" type="image/webp">
                                    <?php endif; ?>
                                    <img src="<?php echo e($featuredPost->getOptimizedImageUrl($featuredPost->featured_image, 'large')); ?>"
                                         alt="<?php echo e($featuredPost->title); ?>"
                                         class="w-full h-64 object-cover hover-lift">
                                </picture>
                            <?php else: ?>
                                <div class="w-full h-64 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <div class="flex items-center space-x-2 mb-4">
                                <?php if($featuredPost->category): ?>
                                    <span class="px-3 py-1 bg-blue-100 text-blue-600 text-sm rounded-full"><?php echo e($featuredPost->category->name); ?></span>
                                <?php endif; ?>
                                <span class="text-gray-500 text-sm"><?php echo e($featuredPost->formatted_published_date); ?></span>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">
                                <?php echo e($featuredPost->title); ?>

                            </h3>
                            <p class="text-gray-600 mb-6 leading-relaxed">
                                <?php echo e($featuredPost->excerpt); ?>

                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-sm"><?php echo e(substr($featuredPost->author->first_name, 0, 1)); ?><?php echo e(substr($featuredPost->author->last_name, 0, 1)); ?></span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($featuredPost->author->first_name); ?> <?php echo e($featuredPost->author->last_name); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo e($featuredPost->reading_time); ?> min read</div>
                                    </div>
                                </div>
                                <a href="<?php echo e(route('blog.show', ['post' => $featuredPost->slug, 'locale' => app()->getLocale()])); ?>" class="text-blue-600 font-semibold hover:text-blue-700 transition-colors inline-flex items-center">
                                    Read More
                                    <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </article>
            </div>
        </div>
    </section>
<?php endif; ?>

<!-- Categories Filter -->
<section class="py-12 bg-gray-50 border-b border-gray-200">
    <div class="container mx-auto px-4">
        <div class="flex flex-wrap justify-center gap-4">
            <button class="category-btn active" data-category="all">All Posts</button>
            <?php
                $categories = \App\Models\BlogCategory::active()->ordered()->get();
            ?>
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button class="category-btn" data-category="<?php echo e($category->slug); ?>"><?php echo e($category->name); ?></button>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('blog.featured', ['locale' => app()->getLocale()])); ?>" class="category-btn" data-category="featured">Featured</a>
            <a href="<?php echo e(route('blog.popular', ['locale' => app()->getLocale()])); ?>" class="category-btn" data-category="popular">Popular</a>
        </div>
    </div>
</section>

<!-- Blog Posts Grid -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <?php if($posts->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="blog-grid">
                <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($post->id !== ($featuredPost->id ?? null)): ?>
                        <article class="blog-card card-hover" data-category="<?php echo e($post->category?->slug ?? 'uncategorized'); ?> <?php if($post->is_featured): ?> featured <?php endif; ?>">
                            <div class="relative overflow-hidden rounded-lg mb-6">
                                <?php if($post->featured_image): ?>
                                    <picture>
                                        <?php if($post->getWebPImageUrl($post->featured_image)): ?>
                                            <source srcset="<?php echo e($post->getWebPImageUrl($post->featured_image)); ?>" type="image/webp">
                                        <?php endif; ?>
                                        <img src="<?php echo e($post->getOptimizedImageUrl($post->featured_image, 'medium')); ?>"
                                             alt="<?php echo e($post->title); ?>"
                                             class="w-full h-48 object-cover hover-lift">
                                    </picture>
                                <?php elseif($post->first_gallery_image_url): ?>
                                    <picture>
                                        <?php if($post->getWebPImageUrl($post->gallery_images[0])): ?>
                                            <source srcset="<?php echo e($post->getWebPImageUrl($post->gallery_images[0])); ?>" type="image/webp">
                                        <?php endif; ?>
                                        <img src="<?php echo e($post->getOptimizedImageUrl($post->gallery_images[0], 'medium')); ?>"
                                             alt="<?php echo e($post->title); ?>"
                                             class="w-full h-48 object-cover hover-lift">
                                    </picture>
                                <?php else: ?>
                                    <div class="w-full h-48 bg-gradient-to-br from-gray-400 to-gray-500 flex items-center justify-center">
                                        <svg class="w-12 h-12 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>

                                <?php if($post->total_image_count > 1): ?>
                                    <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?php echo e($post->total_image_count); ?>

                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center space-x-2">
                                    <?php if($post->category): ?>
                                        <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded"><?php echo e($post->category->name); ?></span>
                                    <?php endif; ?>
                                    <?php if($post->is_featured): ?>
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded">Featured</span>
                                    <?php endif; ?>
                                    <span class="text-gray-500 text-xs"><?php echo e($post->formatted_published_date); ?></span>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                                    <a href="<?php echo e(route('blog.show', ['post' => $post->slug, 'locale' => app()->getLocale()])); ?>"><?php echo e($post->title); ?></a>
                                </h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    <?php echo e($post->excerpt); ?>

                                </p>

                                <?php if($post->services()->count() > 0): ?>
                                    <div class="flex flex-wrap gap-1">
                                        <?php $__currentLoopData = $post->services()->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"><?php echo e($service->name); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($post->services()->count() > 2): ?>
                                            <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">+<?php echo e($post->services()->count() - 2); ?> more</span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="flex items-center justify-between pt-3">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                            <span class="text-white font-semibold text-xs"><?php echo e(substr($post->author->first_name, 0, 1)); ?><?php echo e(substr($post->author->last_name, 0, 1)); ?></span>
                                        </div>
                                        <span class="text-sm text-gray-600"><?php echo e($post->author->first_name); ?> <?php echo e($post->author->last_name); ?></span>
                                    </div>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span><?php echo e($post->reading_time); ?> min read</span>
                                        <?php if($post->view_count > 0): ?>
                                            <span>•</span>
                                            <span><?php echo e(number_format($post->view_count)); ?> views</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </article>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Blog Posts Yet</h3>
                <p class="text-gray-600">We're working on some amazing content. Check back soon!</p>
            </div>
        <?php endif; ?>

        <!-- Pagination -->
        <?php if($posts->hasPages()): ?>
            <div class="flex justify-center mt-12">
                <?php echo e($posts->links('pagination::tailwind')); ?>

            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-4">
            Stay Updated
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-2xl mx-auto">
            Subscribe to our newsletter and get the latest insights, tutorials, and industry trends delivered to your inbox.
        </p>
        <form class="max-w-md mx-auto flex gap-4">
            <input type="email" placeholder="Enter your email" class="form-input flex-1 text-gray-900">
            <button type="submit" class="btn-primary bg-white text-blue-600 hover:bg-blue-50 whitespace-nowrap">
                Subscribe
            </button>
        </form>
    </div>
</section>

<?php $__env->startPush('styles'); ?>
<style>
.category-btn {
    @apply px-4 py-2 rounded-lg text-sm font-medium transition-colors;
    @apply bg-white text-gray-600 hover:bg-gray-50;
}

.category-btn.active {
    @apply bg-blue-600 text-white;
}

.blog-card {
    transition: transform 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-4px);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    const blogCards = document.querySelectorAll('.blog-card');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            
            // Update active button
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter blog posts
            blogCards.forEach(card => {
                const categories = card.getAttribute('data-category');
                
                if (category === 'all' || categories.includes(category)) {
                    card.style.display = 'block';
                    card.classList.add('animate-fade-in');
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/pages/blog/index.blade.php ENDPATH**/ ?>