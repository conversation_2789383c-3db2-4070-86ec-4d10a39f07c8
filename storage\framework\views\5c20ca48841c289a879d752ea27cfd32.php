<?php
// Calculate average rating from all clients
function calculateAverageRating($clients) {
    if ($clients->isEmpty()) return 0;

    $totalRating = $clients->sum('rating');
    $ratedClients = $clients->where('rating', '>', 0)->count();

    return $ratedClients > 0 ? round($totalRating / $ratedClients, 1) : 0;
}

// Generate professional gradient colors (not used in new design but kept for compatibility)
function getProfessionalGradient($index) {
    $gradients = [
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
        'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
    ];

    return $gradients[$index % count($gradients)];
}

$averageRating = calculateAverageRating($clients);
$totalClients = $clients->count();
?>

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'clients' => collect(),
    'autoplay' => true,
    'interval' => 5000,
    'enableTouch' => true,
    'enableKeyboard' => true,
    'title' => 'Our Valued Clients',
    'subtitle' => 'Trusted by industry leaders worldwide'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'clients' => collect(),
    'autoplay' => true,
    'interval' => 5000,
    'enableTouch' => true,
    'enableKeyboard' => true,
    'title' => 'Our Valued Clients',
    'subtitle' => 'Trusted by industry leaders worldwide'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php if($clients->count() > 0): ?>
<section class="py-20 bg-gradient-to-br from-gray-50 to-white">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Our Valued <span class="text-blue-600">Clients</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8"><?php echo e($subtitle); ?></p>

            <!-- Client Statistics -->
            <div class="flex justify-center items-center space-x-8 mb-8">
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600"><?php echo e($totalClients); ?>+</div>
                    <div class="text-sm text-gray-500">Happy Clients</div>
                </div>
                <div class="text-center">
                    <div class="flex items-center justify-center mb-1">
                        <span class="text-3xl font-bold text-yellow-500 mr-2"><?php echo e($averageRating); ?></span>
                        <div class="flex">
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <svg class="w-5 h-5 <?php echo e($i <= $averageRating ? 'text-yellow-400' : 'text-gray-300'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            <?php endfor; ?>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500">Average Rating</div>
                </div>
            </div>
        </div>

        <!-- Professional Client Carousel -->
        <div class="relative max-w-7xl mx-auto">
            <div class="client-carousel-container overflow-hidden"
                 data-autoplay="<?php echo e($autoplay ? 'true' : 'false'); ?>"
                 data-interval="<?php echo e($interval); ?>"
                 data-total-items="<?php echo e($clients->count()); ?>">

                <!-- Carousel Track -->
                <div class="carousel-track flex transition-transform duration-500 ease-in-out" style="transform: translateX(0%);">
                    <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="carousel-slide flex-shrink-0 w-full sm:w-1/2 lg:w-1/3 xl:w-1/4 px-4"
                         data-index="<?php echo e($index); ?>">

                        <!-- Client Card -->
                        <div class="client-card bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 h-full border border-gray-100 cursor-pointer">

                            <!-- Client Logo -->
                            <div class="client-logo-wrapper mb-4 text-center">
                                <?php if($client->logo_path): ?>
                                    <img src="<?php echo e($client->logo_url); ?>"
                                         alt="<?php echo e($client->company); ?> Logo"
                                         class="client-logo w-16 h-16 object-contain mx-auto rounded-lg">
                                <?php else: ?>
                                    <div class="client-logo-placeholder w-16 h-16 mx-auto rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                                        <span class="text-xl font-bold text-blue-600"><?php echo e(substr($client->company ?? $client->name, 0, 1)); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Client Info (Always Visible) -->
                            <div class="text-center mb-4">
                                <h3 class="text-lg font-bold text-gray-900 mb-1 line-clamp-2"><?php echo e($client->company ?? $client->name); ?></h3>
                                <?php if($client->industry): ?>
                                <p class="text-gray-500 text-sm"><?php echo e($client->industry); ?></p>
                                <?php endif; ?>
                            </div>

                            <!-- Rating (Always Visible) -->
                            <?php if($client->rating): ?>
                            <div class="flex justify-center mb-4">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <svg class="w-4 h-4 <?php echo e($i <= $client->rating ? 'text-yellow-400' : 'text-gray-300'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                <?php endfor; ?>
                                <span class="ml-2 text-sm text-gray-600">(<?php echo e($client->rating); ?>)</span>
                            </div>
                            <?php endif; ?>

                            <!-- Expandable Details (Hidden by default) -->
                            <div class="client-details hidden">
                                <!-- Contact Person -->
                                <?php if($client->company && $client->name !== $client->company): ?>
                                <div class="text-center mb-3">
                                    <p class="text-gray-700 text-sm font-medium"><?php echo e($client->name); ?></p>
                                </div>
                                <?php endif; ?>

                                <!-- Testimonial -->
                                <?php if($client->testimonial): ?>
                                <blockquote class="text-gray-600 text-sm italic text-center mb-4 px-2">
                                    "<?php echo e(Str::limit($client->testimonial, 120)); ?>"
                                </blockquote>
                                <?php endif; ?>

                                <!-- Project Status -->
                                <div class="text-center mb-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        <?php echo e($client->project_status === 'completed' ? 'bg-green-100 text-green-800' :
                                           ($client->project_status === 'ongoing' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800')); ?>">
                                        <?php echo e(ucfirst($client->project_status)); ?>

                                    </span>
                                </div>
                            </div>

                            <!-- Expand Button -->
                            <div class="text-center mt-auto">
                                <button class="expand-btn text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200 focus:outline-none">
                                    <span class="expand-text">View Details</span>
                                    <svg class="expand-icon w-4 h-4 inline-block ml-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Navigation Controls -->
                <button class="carousel-nav carousel-prev absolute left-4 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 z-10 focus:outline-none focus:ring-2 focus:ring-blue-500" aria-label="Previous clients">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>

                <button class="carousel-nav carousel-next absolute right-4 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200 z-10 focus:outline-none focus:ring-2 focus:ring-blue-500" aria-label="Next clients">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Indicators -->
                <div class="flex justify-center mt-8 space-x-2">
                    <?php $__currentLoopData = $clients->chunk(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chunkIndex => $chunk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <button class="carousel-indicator w-3 h-3 rounded-full transition-all duration-200 <?php echo e($chunkIndex === 0 ? 'bg-blue-600' : 'bg-gray-300 hover:bg-gray-400'); ?>"
                            data-slide="<?php echo e($chunkIndex); ?>"
                            aria-label="Go to clients <?php echo e($chunkIndex * 4 + 1); ?>-<?php echo e(min(($chunkIndex + 1) * 4, $clients->count())); ?>"></button>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
        
        <!-- Client Stats -->
        <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div class="bg-white rounded-lg p-6 shadow-soft">
                <div class="text-3xl font-bold text-primary-600 mb-2"><?php echo e($clients->count()); ?>+</div>
                <div class="text-gray-600">Happy Clients</div>
            </div>
            <div class="bg-white rounded-lg p-6 shadow-soft">
                <div class="text-3xl font-bold text-primary-600 mb-2"><?php echo e($clients->where('project_status', 'completed')->count()); ?>+</div>
                <div class="text-gray-600">Projects Completed</div>
            </div>
            <div class="bg-white rounded-lg p-6 shadow-soft">
                <div class="text-3xl font-bold text-primary-600 mb-2"><?php echo e(number_format($clients->avg('rating'), 1)); ?></div>
                <div class="text-gray-600">Average Rating</div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/professional-carousel.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/professional-carousel.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/components/carousel-3d.blade.php ENDPATH**/ ?>