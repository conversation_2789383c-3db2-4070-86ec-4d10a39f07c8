<tr class="hover:bg-neutral-50 transition-colors duration-200">
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex items-center" style="padding-left: <?php echo e($level * 20); ?>px;">
            <?php if($level > 0): ?>
                <div class="w-4 h-4 border-l-2 border-b-2 border-neutral-300 mr-2"></div>
            <?php endif; ?>
            
            <div class="flex items-center space-x-3">
                <?php if($category->image): ?>
                    <img src="<?php echo e(asset('storage/' . $category->image)); ?>" 
                         alt="<?php echo e($category->name); ?>" 
                         class="w-10 h-10 rounded-lg object-cover">
                <?php else: ?>
                    <div class="w-10 h-10 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                    </div>
                <?php endif; ?>
                
                <div>
                    <div class="text-sm font-medium text-gray-900"><?php echo e($category->name); ?></div>
                    <div class="text-sm text-gray-500"><?php echo e($category->slug); ?></div>
                </div>
            </div>
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-gray-900"><?php echo e($category->products()->count()); ?></div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <?php if($category->is_active): ?>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Active
            </span>
        <?php else: ?>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Inactive
            </span>
        <?php endif; ?>
    </td>

    <td class="px-6 py-4 whitespace-nowrap">
        <button type="button"
                class="toggle-featured-btn relative inline-flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-200 <?php echo e($category->is_featured ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-300 hover:text-yellow-400'); ?>"
                data-category-id="<?php echo e($category->id); ?>"
                data-category-slug="<?php echo e($category->slug); ?>"
                data-featured="<?php echo e($category->is_featured ? 'true' : 'false'); ?>"
                title="<?php echo e($category->is_featured ? 'Remove from featured' : 'Add to featured'); ?>">
            <svg class="w-5 h-5 <?php echo e($category->is_featured ? 'fill-current' : ''); ?>" fill="<?php echo e($category->is_featured ? 'currentColor' : 'none'); ?>" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
            </svg>
        </button>
    </td>

    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-gray-900"><?php echo e($category->sort_order ?? 0); ?></div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div class="flex items-center space-x-2">
            <a href="<?php echo e(route('admin.categories.show', $category)); ?>" 
               class="text-primary-600 hover:text-primary-900 transition-colors duration-200"
               title="View">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
            </a>
            
            <a href="<?php echo e(route('admin.categories.edit', $category)); ?>" 
               class="text-secondary-600 hover:text-secondary-900 transition-colors duration-200"
               title="Edit">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
            </a>
            
            <form method="POST" action="<?php echo e(route('admin.categories.destroy', $category)); ?>" class="inline">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <button type="button" 
                        class="delete-category text-danger-600 hover:text-danger-900 transition-colors duration-200"
                        title="Delete">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                </button>
            </form>
        </div>
    </td>
</tr>


<?php if(isset($category->children_tree) && count($category->children_tree) > 0): ?>
    <?php $__currentLoopData = $category->children_tree; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php echo $__env->make('admin.categories.partials.category-row', ['category' => $child, 'level' => $level + 1], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/admin/categories/partials/category-row.blade.php ENDPATH**/ ?>