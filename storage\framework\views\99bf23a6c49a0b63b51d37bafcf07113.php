<?php $__env->startSection('title', 'Role Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Role Management</h1>
            <p class="text-gray-600">Configure permissions for each role</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('admin.permissions.index')); ?>" 
               class="inline-flex items-center px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Users
            </a>
        </div>
    </div>

    <!-- Roles Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e(ucfirst($role->name)); ?></h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        <?php echo e($role->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                        <?php echo e($role->is_active ? 'Active' : 'Inactive'); ?>

                    </span>
                </div>
                
                <p class="text-gray-600 text-sm mb-4"><?php echo e($role->description); ?></p>
                
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500"><?php echo e($role->users_count); ?> users</span>
                    <button onclick="editRole(<?php echo e($role->id); ?>)" 
                            class="text-primary-600 hover:text-primary-900 font-medium">
                        Edit Permissions
                    </button>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Permission Configuration -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <div class="p-6 border-b border-neutral-200">
            <h2 class="text-lg font-semibold text-gray-900">Permission Configuration</h2>
            <p class="text-gray-600">Configure detailed permissions for each role</p>
        </div>

        <div class="p-6">
            <!-- Role Selector -->
            <div class="mb-6">
                <label for="role-selector" class="block text-sm font-medium text-gray-700 mb-2">
                    Select Role to Configure
                </label>
                <select id="role-selector" 
                        onchange="loadRolePermissions(this.value)"
                        class="w-full max-w-md px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <option value="">Select a role...</option>
                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($role->id); ?>"><?php echo e(ucfirst($role->name)); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>

            <!-- Permission Matrix -->
            <div id="permission-matrix" class="hidden">
                <form id="permission-form">
                    <div class="space-y-6">
                        <?php $__currentLoopData = $availablePermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource => $actions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-neutral-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-md font-semibold text-gray-900">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $resource))); ?>

                                    </h4>
                                    <div class="flex space-x-2">
                                        <button type="button" 
                                                onclick="selectAllPermissions('<?php echo e($resource); ?>')"
                                                class="text-xs px-2 py-1 bg-primary-100 text-primary-700 rounded hover:bg-primary-200">
                                            Select All
                                        </button>
                                        <button type="button" 
                                                onclick="deselectAllPermissions('<?php echo e($resource); ?>')"
                                                class="text-xs px-2 py-1 bg-neutral-100 text-neutral-700 rounded hover:bg-neutral-200">
                                            Deselect All
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                    <?php $__currentLoopData = $actions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="flex items-center space-x-2 cursor-pointer">
                                            <input type="checkbox" 
                                                   name="permissions[<?php echo e($resource); ?>][]" 
                                                   value="<?php echo e($action); ?>"
                                                   class="permission-checkbox rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
                                                   data-resource="<?php echo e($resource); ?>"
                                                   data-action="<?php echo e($action); ?>">
                                            <span class="text-sm text-gray-700"><?php echo e(ucfirst($action)); ?></span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <div class="mt-8 flex justify-end space-x-4">
                        <button type="button" 
                                onclick="resetPermissions()"
                                class="px-4 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors duration-200">
                            Reset
                        </button>
                        <button type="button" 
                                onclick="savePermissions()"
                                class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                            Save Permissions
                        </button>
                    </div>
                </form>
            </div>

            <!-- No Role Selected Message -->
            <div id="no-role-message" class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No Role Selected</h3>
                <p class="mt-1 text-sm text-gray-500">Select a role above to configure its permissions.</p>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
let currentRoleId = null;
let originalPermissions = {};

function editRole(roleId) {
    document.getElementById('role-selector').value = roleId;
    loadRolePermissions(roleId);
}

async function loadRolePermissions(roleId) {
    if (!roleId) {
        document.getElementById('permission-matrix').classList.add('hidden');
        document.getElementById('no-role-message').classList.remove('hidden');
        return;
    }

    currentRoleId = roleId;
    
    try {
        // Get role data
        const role = <?php echo json_encode($roles, 15, 512) ?>.find(r => r.id == roleId);
        if (!role) return;

        // Show permission matrix
        document.getElementById('permission-matrix').classList.remove('hidden');
        document.getElementById('no-role-message').classList.add('hidden');

        // Store original permissions
        originalPermissions = role.permissions || {};

        // Clear all checkboxes first
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Set current permissions
        Object.keys(originalPermissions).forEach(resource => {
            const actions = originalPermissions[resource];
            actions.forEach(action => {
                const checkbox = document.querySelector(`input[data-resource="${resource}"][data-action="${action}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
        });

    } catch (error) {
        console.error('Error loading role permissions:', error);
        showNotification('Error loading role permissions', 'error');
    }
}

function selectAllPermissions(resource) {
    document.querySelectorAll(`input[data-resource="${resource}"]`).forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselectAllPermissions(resource) {
    document.querySelectorAll(`input[data-resource="${resource}"]`).forEach(checkbox => {
        checkbox.checked = false;
    });
}

function resetPermissions() {
    if (!currentRoleId) return;
    loadRolePermissions(currentRoleId);
}

async function savePermissions() {
    if (!currentRoleId) {
        showNotification('Please select a role first', 'warning');
        return;
    }

    // Collect selected permissions
    const permissions = {};
    document.querySelectorAll('.permission-checkbox:checked').forEach(checkbox => {
        const resource = checkbox.dataset.resource;
        const action = checkbox.dataset.action;
        
        if (!permissions[resource]) {
            permissions[resource] = [];
        }
        permissions[resource].push(action);
    });

    try {
        const response = await fetch(`/admin/permissions/roles/${currentRoleId}/permissions`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ permissions })
        });

        const data = await response.json();
        
        if (data.success) {
            showNotification('Permissions updated successfully', 'success');
            originalPermissions = permissions;
        } else {
            showNotification(data.message || 'Failed to update permissions', 'error');
        }
    } catch (error) {
        console.error('Error saving permissions:', error);
        showNotification('An error occurred while saving permissions', 'error');
    }
}

function showNotification(message, type) {
    // Implementation depends on your notification system
    // This is a simple alert for now
    alert(message);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Auto-select first role if available
    const roleSelector = document.getElementById('role-selector');
    if (roleSelector.options.length > 1) {
        // Optionally auto-select the first role
        // roleSelector.selectedIndex = 1;
        // loadRolePermissions(roleSelector.value);
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/admin/permissions/roles.blade.php ENDPATH**/ ?>