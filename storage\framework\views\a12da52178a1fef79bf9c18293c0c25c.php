
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['type' => 'organization', 'data' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['type' => 'organization', 'data' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    // Helper to encode JSON-LD with readable formatting and unescaped slashes
    $ldJson = function ($arr) {
        return json_encode($arr, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    };

    // Build structured data based on type
    $structuredData = [];

    switch ($type) {
        case 'organization':
            $structuredData = [
                '@context' => 'https://schema.org',
                '@type' => 'Organization',
                'name' => $data['name'] ?? __('common.company_name'),
                'description' => $data['description'] ?? __('common.company_description'),
                'url' => $data['url'] ?? url('/'),
                'logo' => $data['logo'] ?? asset('images/logo.png'),
                'email' => $data['email'] ?? config('company.contact.info_email', '<EMAIL>'),
                'telephone' => $data['telephone'] ?? config('company.contact.phone', '+27-11-123-4567'),
                'address' => [
                    '@type' => 'PostalAddress',
                    'streetAddress' => $data['address']['street'] ?? config('company.address.street', '123 Business Street'),
                    'addressLocality' => $data['address']['city'] ?? config('company.address.city', 'Cape Town'),
                    'addressRegion' => $data['address']['state'] ?? config('company.address.state', 'Western Cape'),
                    'postalCode' => $data['address']['postal_code'] ?? config('company.address.postal_code', '8001'),
                    'addressCountry' => $data['address']['country'] ?? config('company.address.country', 'ZA')
                ],
                'contactPoint' => [
                    '@type' => 'ContactPoint',
                    'telephone' => $data['telephone'] ?? config('company.contact.phone'),
                    'contactType' => 'customer service',
                    'availableLanguage' => $data['languages'] ?? ['English', 'Afrikaans', 'French', 'Spanish']
                ],
                'sameAs' => $data['social_media'] ?? [
                    'https://www.facebook.com/chisolution',
                    'https://www.linkedin.com/company/chisolution',
                    'https://twitter.com/chisolution'
                ]
            ];
            break;

        case 'breadcrumb':
            $items = $data['items'] ?? [];
            $listItems = [];

            foreach ($items as $index => $item) {
                $listItems[] = [
                    '@type' => 'ListItem',
                    'position' => $index + 1,
                    'name' => $item['name'],
                    'item' => $item['url'] ?? null
                ];
            }

            $structuredData = [
                '@context' => 'https://schema.org',
                '@type' => 'BreadcrumbList',
                'itemListElement' => $listItems
            ];
            break;

        case 'service':
            $structuredData = [
                '@context' => 'https://schema.org',
                '@type' => 'Service',
                'name' => $data['name'] ?? 'Professional Services',
                'description' => $data['description'] ?? 'High-quality professional services',
                'provider' => [
                    '@type' => 'Organization',
                    'name' => $data['provider']['name'] ?? __('common.company_name'),
                    'url' => $data['provider']['url'] ?? url('/'),
                    'logo' => $data['provider']['logo'] ?? asset('images/logo.png'),
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressCountry' => $data['provider']['country'] ?? 'ZA',
                        'addressRegion' => $data['provider']['region'] ?? 'South Africa'
                    ],
                    'contactPoint' => [
                        '@type' => 'ContactPoint',
                        'telephone' => $data['provider']['phone'] ?? config('company.contact.phone'),
                        'contactType' => 'customer service',
                        'availableLanguage' => $data['provider']['languages'] ?? ['English', 'Afrikaans', 'French', 'Spanish']
                    ]
                ],
                'serviceType' => $data['service_type'] ?? 'Professional Services',
                'category' => $data['category'] ?? 'Technology Services',
                'areaServed' => $data['area_served'] ?? 'Worldwide',
                'offers' => [
                    '@type' => 'Offer',
                    'availability' => 'https://schema.org/InStock'
                ]
            ];

            // Add service catalog if provided
            if (isset($data['catalog']) && is_array($data['catalog'])) {
                $catalogItems = [];
                foreach ($data['catalog'] as $item) {
                    $catalogItems[] = [
                        '@type' => 'Offer',
                        'itemOffered' => [
                            '@type' => 'Service',
                            'name' => $item['name'],
                            'description' => $item['description']
                        ]
                    ];
                }

                $structuredData['hasOfferCatalog'] = [
                    '@type' => 'OfferCatalog',
                    'name' => $data['catalog_name'] ?? 'Service Catalog',
                    'itemListElement' => $catalogItems
                ];
            }
            break;

        case 'product':
            $structuredData = [
                '@context' => 'https://schema.org',
                '@type' => 'Product',
                'name' => $data['name'] ?? 'Product',
                'description' => $data['description'] ?? 'High-quality product',
                'image' => $data['images'] ?? [asset('images/default-product.jpg')],
                'brand' => [
                    '@type' => 'Brand',
                    'name' => $data['brand'] ?? __('common.company_name')
                ],
                'manufacturer' => [
                    '@type' => 'Organization',
                    'name' => $data['manufacturer'] ?? __('common.company_name')
                ]
            ];

            // Add SKU if provided
            if (isset($data['sku'])) {
                $structuredData['sku'] = $data['sku'];
            }

            // Add offers if provided
            if (isset($data['offers'])) {
                $structuredData['offers'] = [
                    '@type' => 'Offer',
                    'price' => $data['offers']['price'] ?? '0',
                    'priceCurrency' => $data['offers']['currency'] ?? 'ZAR',
                    'availability' => $data['offers']['availability'] ?? 'https://schema.org/InStock',
                    'seller' => [
                        '@type' => 'Organization',
                        'name' => $data['offers']['seller'] ?? __('common.company_name')
                    ]
                ];
            }

            // Add reviews if provided
            if (isset($data['reviews']) && is_array($data['reviews'])) {
                $reviewItems = [];
                $totalRating = 0;
                $reviewCount = count($data['reviews']);

                foreach ($data['reviews'] as $review) {
                    $reviewItems[] = [
                        '@type' => 'Review',
                        'reviewRating' => [
                            '@type' => 'Rating',
                            'ratingValue' => $review['rating'],
                            'bestRating' => '5'
                        ],
                        'author' => [
                            '@type' => 'Person',
                            'name' => $review['author']
                        ],
                        'reviewBody' => $review['body'] ?? ''
                    ];
                    $totalRating += $review['rating'];
                }

                $structuredData['review'] = $reviewItems;
                $structuredData['aggregateRating'] = [
                    '@type' => 'AggregateRating',
                    'ratingValue' => round($totalRating / $reviewCount, 1),
                    'reviewCount' => $reviewCount
                ];
            }
            break;

        case 'webpage':
            $structuredData = [
                '@context' => 'https://schema.org',
                '@type' => 'WebPage',
                'name' => $data['name'] ?? 'Web Page',
                'description' => $data['description'] ?? 'Professional web page',
                'url' => $data['url'] ?? url()->current(),
                'inLanguage' => $data['language'] ?? app()->getLocale(),
                'isPartOf' => [
                    '@type' => 'WebSite',
                    'name' => $data['website_name'] ?? __('common.company_name'),
                    'url' => $data['website_url'] ?? url('/')
                ],
                'publisher' => [
                    '@type' => 'Organization',
                    'name' => $data['publisher'] ?? __('common.company_name'),
                    'logo' => [
                        '@type' => 'ImageObject',
                        'url' => $data['publisher_logo'] ?? asset('images/logo.png')
                    ]
                ]
            ];

            // Add breadcrumb if provided
            if (isset($data['breadcrumb']) && is_array($data['breadcrumb'])) {
                $breadcrumbItems = [];
                foreach ($data['breadcrumb'] as $index => $item) {
                    $breadcrumbItems[] = [
                        '@type' => 'ListItem',
                        'position' => $index + 1,
                        'name' => $item['name'],
                        'item' => $item['url'] ?? null
                    ];
                }

                $structuredData['breadcrumb'] = [
                    '@type' => 'BreadcrumbList',
                    'itemListElement' => $breadcrumbItems
                ];
            }
            break;

        default:
            // Custom type - use data as-is with context
            $structuredData = array_merge([
                '@context' => 'https://schema.org'
            ], $data);
            break;
    }
?>

<?php if(!empty($structuredData)): ?>
<script type="application/ld+json">
<?php echo $ldJson($structuredData); ?>

</script>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/components/structured-data.blade.php ENDPATH**/ ?>