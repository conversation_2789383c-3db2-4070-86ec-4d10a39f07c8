<?php $__env->startSection('title', __('errors.404_title', ['default' => 'Page Not Found']) . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', __('errors.404_description', ['default' => 'The page you are looking for could not be found.'])); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-blue-100">
            <svg class="h-16 w-16 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
        </div>

        <!-- Error Code -->
        <div>
            <h1 class="text-6xl font-bold text-gray-900">404</h1>
            <h2 class="mt-2 text-3xl font-extrabold text-gray-900">
                <?php echo e(__('errors.404_title', ['default' => 'Page Not Found'])); ?>

            </h2>
            <p class="mt-2 text-base text-gray-600">
                <?php echo e(__('errors.404_description', ['default' => 'Sorry, we couldn\'t find the page you\'re looking for.'])); ?>

            </p>
        </div>

        <!-- Actions -->
        <div class="mt-8 space-y-4">
            <a href="<?php echo e(route('home', ['locale' => app()->getLocale()])); ?>" class="btn-primary inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                </svg>
                <?php echo e(__('errors.go_home', ['default' => 'Go to Homepage'])); ?>

            </a>
            <div>
                <button onclick="history.back()" class="text-primary-600 hover:text-primary-800 font-medium">
                    <?php echo e(__('errors.go_back', ['default' => 'Go Back'])); ?>

                </button>
            </div>
        </div>

        <!-- Helpful Links -->
        <div class="mt-8 pt-8 border-t border-gray-200">
            <p class="text-sm font-medium text-gray-900 mb-4"><?php echo e(__('errors.helpful_links', ['default' => 'Helpful Links'])); ?></p>
            <div class="flex flex-wrap justify-center gap-4">
                <a href="<?php echo e(route('services.index', ['locale' => app()->getLocale()])); ?>" class="text-sm text-primary-600 hover:text-primary-800">
                    <?php echo e(__('common.services')); ?>

                </a>
                <a href="<?php echo e(route('projects.index', ['locale' => app()->getLocale()])); ?>" class="text-sm text-primary-600 hover:text-primary-800">
                    <?php echo e(__('common.projects')); ?>

                </a>
                <a href="<?php echo e(route('contact', ['locale' => app()->getLocale()])); ?>" class="text-sm text-primary-600 hover:text-primary-800">
                    <?php echo e(__('common.contact')); ?>

                </a>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="mt-4">
            <p class="text-sm text-gray-600">
                <?php echo e(__('errors.need_help', ['default' => 'Need help?'])); ?>

                <a href="<?php echo e(route('contact', ['locale' => app()->getLocale()])); ?>" class="text-primary-600 hover:text-primary-800 font-medium">
                    <?php echo e(__('errors.contact_support', ['default' => 'Contact Support'])); ?>

                </a>
            </p>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/errors/404.blade.php ENDPATH**/ ?>