<?php $__env->startSection('title', __('common.contact_us') . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', __('contact.hero_description')); ?>
<?php $__env->startSection('meta_keywords', __('common.contact_us') . ', ' . __('common.company_name') . ', ' . __('common.services')); ?>

<?php $__env->startPush('structured_data'); ?>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "ContactPage",
  "name": @json(__('common.contact_us')),
  "description": @json(__('contact.hero_description')),
  "url": @json(url('/contact')),
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": @json(url('/contact'))
  }
}
</script>

<?php $__env->stopPush(); ?>


<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="heading-1 text-white mb-6">
                <?php echo __('contact.hero_title'); ?>

            </h1>
            <p class="text-lead text-blue-100 mb-8">
                <?php echo e(__('contact.hero_description')); ?>

            </p>
        </div>
    </div>
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div class="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
                <div class="text-center mb-8">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                    </div>
                    <h2 class="heading-2 mb-4">
                        <?php echo __('contact.send_message'); ?>

                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo e(__('contact.form_description')); ?>

                    </p>
                    <div class="mt-6">
                        <a href="<?php echo e(route('apply.project', ['locale' => app()->getLocale()])); ?>"
                           class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <?php echo e(__('contact.start_project_application')); ?>

                        </a>
                    </div>
                </div>

                <form id="contact-form" class="space-y-8" action="<?php echo e(route('contact.submit', ['locale' => app()->getLocale()])); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>

                    <!-- Success/Error Messages -->
                    <div id="form-messages" class="hidden"></div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div class="floating-input-group">
                            <input type="text" id="name" name="name" required class="floating-input peer" placeholder=" ">
                            <label for="name" class="floating-label"><?php echo __('contact.name'); ?> *</label>
                        </div>
                        <div class="floating-input-group">
                            <input type="email" id="email" name="email" required class="floating-input peer" placeholder=" ">
                            <label for="email" class="floating-label"><?php echo __('contact.email'); ?> *</label>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div class="floating-input-group">
                            <input type="tel" id="phone" name="phone" class="floating-input peer" placeholder=" ">
                            <label for="phone" class="floating-label"><?php echo __('contact.phone'); ?></label>
                        </div>
                        <div class="floating-input-group">
                            <input type="text" id="company" name="company" class="floating-input peer" placeholder=" ">
                            <label for="company" class="floating-label"><?php echo __('contact.company'); ?></label>
                        </div>
                    </div>

                    <div class="floating-input-group">
                        <select id="service" name="service" class="floating-input peer">
                            <option value=""></option>
                            <option value="ai-services"><?php echo __('contact.service_ai_services'); ?></option>
                            <option value="web-development"><?php echo __('contact.service_web_development'); ?></option>
                            <option value="mobile-app-development"><?php echo __('contact.service_mobile_app_development'); ?></option>
                            <option value="ecommerce-development"><?php echo __('contact.service_ecommerce_development'); ?></option>
                            <option value="digital-marketing"><?php echo __('contact.service_digital_marketing'); ?></option>
                            <option value="seo-services"><?php echo __('contact.service_seo_services'); ?></option>
                            <option value="maintenance-support"><?php echo __('contact.service_maintenance_support'); ?></option>
                            <option value="other"><?php echo __('contact.service_other'); ?></option>
                        </select>
                        <label for="service" class="floating-label"><?php echo __('contact.service_interested'); ?></label>
                    </div>

                    <div class="floating-input-group">
                        <textarea id="message" name="message" rows="5" required class="floating-input peer resize-vertical" placeholder=" "></textarea>
                        <label for="message" class="floating-label"><?php echo __('contact.project_details'); ?> *</label>
                    </div>

                    <!-- File Upload Section -->
                    <div class="space-y-4">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-900"><?php echo __('contact.project_attachments'); ?></h3>
                            <span class="text-sm text-gray-500">(Optional)</span>
                        </div>

                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors duration-200">
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="mt-4">
                                    <label for="attachments" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">
                                            <?php echo __('contact.upload_project_files'); ?>

                                        </span>
                                        <span class="mt-1 block text-xs text-gray-500">
                                            <?php echo __('contact.file_types_size'); ?>

                                        </span>
                                    </label>
                                    <input id="attachments" name="attachments[]" type="file" class="sr-only" multiple
                                           accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.zip,.jpg,.jpeg,.png,.webp">
                                </div>
                                <p class="text-xs text-gray-500 mt-2">
                                    <?php echo __('contact.file_scan_security'); ?>

                                </p>
                            </div>
                        </div>

                        <!-- File Preview Area -->
                        <div id="file-preview" class="hidden space-y-2">
                            <h4 class="text-sm font-medium text-gray-900"><?php echo __('contact.selected_files'); ?>:</h4>
                            <div id="file-list" class="space-y-2"></div>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="newsletter" name="newsletter" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="newsletter" class="ml-2 text-sm text-gray-600">
                            <?php echo __('contact.newsletter_consent'); ?>

                        </label>
                    </div>

                    <button type="submit" id="submit-btn" class="btn-primary w-full transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <span class="submit-text">
                            <?php echo __('contact.send_message'); ?>

                            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                            </svg>
                        </span>
                        <span class="loading-text hidden">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <?php echo __('contact.sending'); ?>...
                        </span>
                    </button>
                </form>
            </div>

            <!-- Contact Information -->
            <div class="space-y-8">
                <div class="text-center lg:text-left">
                    <h2 class="heading-2 mb-4">
                        <?php echo __('contact.contact_information'); ?>

                    </h2>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo __('contact.contact_information_description'); ?>

                    </p>
                </div>

                <div class="space-y-6">
                    <!-- Phone -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-1"><?php echo __('contact.phone'); ?></h3>
                                <p class="text-gray-600 font-medium"><?php echo e(config('company.contact.phone')); ?></p>
                                <p class="text-sm text-gray-500"><?php echo e(config('company.business_hours.weekdays')); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-1"><?php echo __('contact.email'); ?></h3>
                                <p class="text-gray-600 font-medium"><?php echo e(config('company.contact.email')); ?></p>
                                <p class="text-sm text-gray-500">We'll respond within <?php echo e(config('company.response_time.general')); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-1"><?php echo __('contact.office'); ?></h3>
                                <p class="text-gray-600 font-medium"><?php echo e(config('company.address.street')); ?><br><?php echo e(config('company.address.city')); ?>, <?php echo e(config('company.address.country')); ?></p>
                                <p class="text-sm text-gray-500"><?php echo e(config('company.business_hours.weekend')); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo __('contact.follow_us'); ?></h3>
                                <div class="flex space-x-3">
                                    <!-- LinkedIn -->
                                    <a href="#" class="social-icon group" title="LinkedIn">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                        </svg>
                                    </a>

                                    <!-- X (Twitter) -->
                                    <a href="#" class="social-icon group" title="X (Twitter)">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                                        </svg>
                                    </a>

                                    <!-- Facebook -->
                                    <a href="#" class="social-icon group" title="Facebook">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                        </svg>
                                    </a>

                                    <!-- Instagram -->
                                    <a href="#" class="social-icon group" title="Instagram">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                        </svg>
                                    </a>
                                </div>
                                <p class="text-sm text-gray-500 mt-3"><?php echo __('contact.stay_updated'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Response Time -->
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo __('contact.quick_response'); ?></h3>
                            <p class="text-gray-700 leading-relaxed">
                                <?php echo __('contact.quick_response_description'); ?>

                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center mb-12">
            <h2 class="heading-2 mb-4"><?php echo e(__('contact.faq_title')); ?></h2>
            <p class="text-gray-600"><?php echo e(__('contact.faq_description')); ?></p>
        </div>
        <div class="max-w-2xl mx-auto">
            <div class="faq-item border-b border-gray-200">
                <button type="button" class="faq-trigger w-full flex justify-between items-center py-4 text-left focus:outline-none">
                    <span class="font-semibold text-lg"><?php echo e(__('contact.faq_1_q')); ?></span>
                    <svg class="faq-icon w-5 h-5 text-blue-600 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/></svg>
                </button>
                <div class="faq-content max-h-0 overflow-hidden transition-all duration-300">
                    <p class="text-gray-700 py-2"><?php echo e(__('contact.faq_1_a')); ?></p>
                </div>
            </div>
            <div class="faq-item border-b border-gray-200">
                <button type="button" class="faq-trigger w-full flex justify-between items-center py-4 text-left focus:outline-none">
                    <span class="font-semibold text-lg"><?php echo e(__('contact.faq_2_q')); ?></span>
                    <svg class="faq-icon w-5 h-5 text-blue-600 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/></svg>
                </button>
                <div class="faq-content max-h-0 overflow-hidden transition-all duration-300">
                    <p class="text-gray-700 py-2"><?php echo e(__('contact.faq_2_a')); ?></p>
                </div>
            </div>
            <div class="faq-item border-b border-gray-200">
                <button type="button" class="faq-trigger w-full flex justify-between items-center py-4 text-left focus:outline-none">
                    <span class="font-semibold text-lg"><?php echo e(__('contact.faq_3_q')); ?></span>
                    <svg class="faq-icon w-5 h-5 text-blue-600 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/></svg>
                </button>
                <div class="faq-content max-h-0 overflow-hidden transition-all duration-300">
                    <p class="text-gray-700 py-2"><?php echo e(__('contact.faq_3_a')); ?></p>
                </div>
            </div>
            <div class="faq-item border-b border-gray-200">
                <button type="button" class="faq-trigger w-full flex justify-between items-center py-4 text-left focus:outline-none">
                    <span class="font-semibold text-lg"><?php echo e(__('contact.faq_4_q')); ?></span>
                    <svg class="faq-icon w-5 h-5 text-blue-600 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/></svg>
                </button>
                <div class="faq-content max-h-0 overflow-hidden transition-all duration-300">
                    <p class="text-gray-700 py-2"><?php echo e(__('contact.faq_4_a')); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('styles'); ?>
<style>
/* Social Icons */
.social-icon {
    @apply w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 hover:bg-blue-600 hover:text-white transition-all duration-300 transform hover:scale-110;
}

/* FAQ Accordion */
.faq-item.active .faq-icon {
    transform: rotate(180deg);
}

.faq-item.active .faq-content {
    max-height: 200px;
}

.faq-item.active .faq-trigger {
    background-color: #f9fafb;
}

/* Floating Label Form Styles */
.floating-input-group {
    position: relative;
    margin-bottom: 2rem;
}

.floating-input {
    width: 100%;
    padding: 1.25rem 0 0.75rem 0;
    border: none;
    border-bottom: 2px solid #e5e7eb;
    background: transparent;
    outline: none;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #374151;
    font-weight: 400;
}

.floating-input:focus {
    border-bottom-color: #3b82f6;
    box-shadow: 0 1px 0 0 #3b82f6;
}

.floating-input:focus ~ .floating-label,
.floating-input:not(:placeholder-shown) ~ .floating-label,
.floating-input.has-value ~ .floating-label {
    transform: translateY(-1.75rem) scale(0.85);
    color: #3b82f6;
    font-weight: 500;
}

.floating-label {
    position: absolute;
    top: 1.25rem;
    left: 0;
    font-size: 1rem;
    color: #9ca3af;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: left top;
    background: transparent;
    z-index: 1;
}

/* Select specific styles */
.floating-input-group select.floating-input {
    padding: 1.25rem 2.5rem 0.75rem 0;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.25em 1.25em;
    cursor: pointer;
}

.floating-input-group select.floating-input:focus ~ .floating-label,
.floating-input-group select.floating-input:valid ~ .floating-label,
.floating-input-group select.floating-input.has-value ~ .floating-label {
    transform: translateY(-1.75rem) scale(0.85);
    color: #3b82f6;
    font-weight: 500;
}

/* Textarea specific styles */
.floating-input-group textarea.floating-input {
    min-height: 140px;
    resize: vertical;
    padding-top: 1.75rem;
    line-height: 1.5;
}

/* Hover effects */
.floating-input-group:hover .floating-input:not(:focus) {
    border-bottom-color: #d1d5db;
}

.floating-input-group:hover .floating-label {
    color: #6b7280;
}

/* Error states */
.floating-input.error {
    border-bottom-color: #ef4444;
}

.floating-input.error ~ .floating-label {
    color: #ef4444;
}

/* Success states */
.floating-input.success {
    border-bottom-color: #10b981;
}

.floating-input.success ~ .floating-label {
    color: #10b981;
}

/* Disabled states */
.floating-input:disabled {
    border-bottom-color: #d1d5db;
    color: #9ca3af;
    cursor: not-allowed;
}

.floating-input:disabled ~ .floating-label {
    color: #d1d5db;
}

/* Animation for form load */
.floating-input-group {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.floating-input-group:nth-child(1) { animation-delay: 0.1s; }
.floating-input-group:nth-child(2) { animation-delay: 0.2s; }
.floating-input-group:nth-child(3) { animation-delay: 0.3s; }
.floating-input-group:nth-child(4) { animation-delay: 0.4s; }
.floating-input-group:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus ring for accessibility */
.floating-input:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

/* Required field indicator */
.floating-label:after {
    content: '';
}

.floating-label[for="full_name"]:after,
.floating-label[for="email"]:after,
.floating-label[for="message"]:after {
    content: ' *';
    color: #ef4444;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Floating Label Form Enhancement
    const floatingInputs = document.querySelectorAll('.floating-input');

    floatingInputs.forEach(input => {
        // Handle initial state for pre-filled inputs
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
        }

        // Handle input events
        input.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });

        // Handle focus events
        input.addEventListener('focus', function() {
            this.classList.add('focused');
        });

        // Handle blur events
        input.addEventListener('blur', function() {
            this.classList.remove('focused');
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });

        // Special handling for select elements
        if (input.tagName === 'SELECT') {
            input.addEventListener('change', function() {
                if (this.value !== '') {
                    this.classList.add('has-value');
                } else {
                    this.classList.remove('has-value');
                }
            });
        }
    });

    // File Upload Handling
    const fileInput = document.getElementById('attachments');
    const filePreview = document.getElementById('file-preview');
    const fileList = document.getElementById('file-list');
    const maxFileSize = 25 * 1024 * 1024; // 25MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'application/zip',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp'
    ];

    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            fileList.innerHTML = '';

            if (files.length === 0) {
                filePreview.classList.add('hidden');
                return;
            }

            let validFiles = [];

            files.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg';

                // Validate file
                let isValid = true;
                let errorMessage = '';

                if (file.size > maxFileSize) {
                    isValid = false;
                    errorMessage = 'File too large (max 25MB)';
                } else if (!allowedTypes.includes(file.type)) {
                    isValid = false;
                    errorMessage = 'File type not allowed';
                }

                if (isValid) {
                    validFiles.push(file);
                }

                const fileInfo = document.createElement('div');
                fileInfo.className = 'flex items-center space-x-3';

                const fileIcon = getFileIcon(file.type);
                const fileSize = formatFileSize(file.size);

                fileInfo.innerHTML = `
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg ${isValid ? 'bg-blue-100' : 'bg-red-100'}">
                        ${fileIcon}
                    </div>
                    <div>
                        <p class="text-sm font-medium ${isValid ? 'text-gray-900' : 'text-red-900'}">${file.name}</p>
                        <p class="text-xs ${isValid ? 'text-gray-500' : 'text-red-500'}">
                            ${isValid ? fileSize : errorMessage}
                        </p>
                    </div>
                `;

                const removeButton = document.createElement('button');
                removeButton.type = 'button';
                removeButton.className = 'text-gray-400 hover:text-red-500 transition-colors duration-200';
                removeButton.innerHTML = `
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                `;

                removeButton.addEventListener('click', function() {
                    fileItem.remove();
                    // Update file input
                    const dt = new DataTransfer();
                    Array.from(fileInput.files).forEach((f, i) => {
                        if (i !== index) dt.items.add(f);
                    });
                    fileInput.files = dt.files;

                    if (fileList.children.length === 0) {
                        filePreview.classList.add('hidden');
                    }
                });

                fileItem.appendChild(fileInfo);
                fileItem.appendChild(removeButton);
                fileList.appendChild(fileItem);
            });

            filePreview.classList.remove('hidden');
        });
    }

    function getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) {
            return '<svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType === 'application/pdf') {
            return '<svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) {
            return '<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) {
            return '<svg class="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType === 'application/zip') {
            return '<svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>';
        } else {
            return '<svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // FAQ Accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const trigger = item.querySelector('.faq-trigger');
        const content = item.querySelector('.faq-content');
        const icon = item.querySelector('.faq-icon');

        trigger.addEventListener('click', function() {
            const isActive = item.classList.contains('active');

            // Close all other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                    otherItem.querySelector('.faq-content').style.maxHeight = null;
                    otherItem.querySelector('.faq-icon').style.transform = '';
                }
            });

            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
                content.style.maxHeight = null;
                icon.style.transform = '';
            } else {
                item.classList.add('active');
                content.style.maxHeight = content.scrollHeight + 'px';
                icon.style.transform = 'rotate(180deg)';
            }
        });
    });

    // AJAX Form submission handling
    const contactForm = document.getElementById('contact-form');
    const submitBtn = document.getElementById('submit-btn');
    const submitText = submitBtn.querySelector('.submit-text');
    const loadingText = submitBtn.querySelector('.loading-text');
    const messagesContainer = document.getElementById('form-messages');

    if (contactForm) {
        contactForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Show loading state
            submitBtn.disabled = true;
            submitText.classList.add('hidden');
            loadingText.classList.remove('hidden');

            // Clear previous messages
            messagesContainer.innerHTML = '';
            messagesContainer.classList.add('hidden');

            // Clear previous error states
            document.querySelectorAll('.border-red-500').forEach(el => {
                el.classList.remove('border-red-500');
            });
            document.querySelectorAll('.text-red-600').forEach(el => {
                if (el.classList.contains('error-message')) {
                    el.remove();
                }
            });

            try {
                const formData = new FormData(contactForm);

                const response = await fetch(contactForm.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Show success message
                    showMessage(data.message, 'success');

                    // Reset form
                    contactForm.reset();

                    // Reset floating labels
                    floatingInputs.forEach(input => {
                        input.classList.remove('has-value');
                    });

                    // Hide file preview
                    if (filePreview) {
                        filePreview.classList.add('hidden');
                        fileList.innerHTML = '';
                    }
                } else {
                    // Show error message
                    showMessage(data.message || 'An error occurred. Please try again.', 'error');

                    // Show field-specific errors
                    if (data.errors) {
                        Object.keys(data.errors).forEach(field => {
                            const input = document.querySelector(`[name="${field}"]`);
                            if (input) {
                                input.classList.add('border-red-500');

                                // Add error message
                                const errorDiv = document.createElement('div');
                                errorDiv.className = 'error-message text-red-600 text-sm mt-1';
                                errorDiv.textContent = data.errors[field][0];
                                input.parentElement.appendChild(errorDiv);
                            }
                        });
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('An error occurred. Please try again.', 'error');
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                submitText.classList.remove('hidden');
                loadingText.classList.add('hidden');
            }
        });
    }

    // Show message function
    function showMessage(message, type) {
        const bgColor = type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800';
        const icon = type === 'success'
            ? '<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>'
            : '<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>';

        messagesContainer.innerHTML = `
            <div class="rounded-md p-4 border ${bgColor}">
                <div class="flex">
                    <div class="flex-shrink-0">
                        ${icon}
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                </div>
            </div>
        `;
        messagesContainer.classList.remove('hidden');

        // Scroll to message
        messagesContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/pages/contact.blade.php ENDPATH**/ ?>