<?php $__env->startSection('title', __('auth.login') . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', __('auth.login_description')); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-container">
    <div class="auth-background">
        <div class="auth-window">
            <div class="auth-header">
                <h1 class="auth-title"><?php echo e(__('auth.welcome_back')); ?></h1>
                <p class="auth-subtitle"><?php echo e(__('auth.login_subtitle')); ?></p>
            </div>

            <form class="auth-form" id="login-form" method="POST" action="<?php echo e(route('login')); ?>">
                <?php echo csrf_field(); ?>

                <!-- Email Field -->
                <div class="floating-input-group">
                    <input 
                        type="email" 
                        name="email" 
                        id="email"
                        class="floating-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        value="<?php echo e(old('email')); ?>"
                        required 
                        autocomplete="email"
                        placeholder=" "
                    >
                    <label for="email" class="floating-label"><?php echo e(__('auth.email')); ?></label>
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="error-message"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Password Field -->
                <div class="floating-input-group">
                    <input 
                        type="password" 
                        name="password" 
                        id="password"
                        class="floating-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        required 
                        autocomplete="current-password"
                        placeholder=" "
                    >
                    <label for="password" class="floating-label"><?php echo e(__('auth.password')); ?></label>
                    <div class="password-toggle">
                        <button type="button" class="password-toggle-btn" data-target="password">
                            <i class="password-icon" data-show="👁️" data-hide="🙈">👁️</i>
                        </button>
                    </div>
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="error-message"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Remember Me -->
                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                        <span class="checkbox-custom"></span>
                        <span class="checkbox-text"><?php echo e(__('auth.remember_me')); ?></span>
                    </label>
                    
                    <a href="<?php echo e(route('password.request')); ?>" class="forgot-password-link">
                        <?php echo e(__('auth.forgot_password')); ?>

                    </a>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="auth-submit-btn" id="login-btn">
                    <span class="btn-text"><?php echo e(__('auth.login')); ?></span>
                    <span class="btn-loading hidden">
                        <svg class="animate-spin h-5 w-5" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <?php echo e(__('auth.logging_in')); ?>

                    </span>
                </button>

                <!-- Form Messages -->
                <div class="form-messages" id="form-messages"></div>

                <!-- Register Link -->
                <div class="auth-footer">
                    <span class="auth-footer-text"><?php echo e(__('auth.no_account')); ?></span>
                    <a href="<?php echo e(route('register')); ?>" class="auth-footer-link"><?php echo e(__('auth.create_account')); ?></a>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/auth-forms.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize login form with AJAX (no client-side validation)
    const loginForm = new AuthForm('login-form', {
        submitButton: 'login-btn',
        loadingText: '<?php echo e(__("auth.logging_in")); ?>',
        successRedirect: '<?php echo e(route("dashboard")); ?>',
        validateOnSubmit: false, // No client-side validation for login
        messages: {
            networkError: '<?php echo e(__("auth.network_error")); ?>',
            serverError: '<?php echo e(__("auth.server_error")); ?>',
            validationError: '<?php echo e(__("auth.validation_error")); ?>'
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/auth/login.blade.php ENDPATH**/ ?>