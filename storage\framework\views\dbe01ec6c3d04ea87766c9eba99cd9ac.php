<?php
use Illuminate\Support\Facades\Storage;
?>

<!-- Top Bar (Desktop Only) -->
<div class="hidden lg:block bg-gray-50 border-b border-gray-200">
    <div class="container mx-auto px-4 py-2">
        <div class="flex justify-between items-center text-sm">
            <!-- Contact Info & Careers -->
            <div class="flex items-center space-x-6 text-gray-600">
                <a href="mailto:<?php echo e(config('company.contact.info_email')); ?>" class="flex items-center hover:text-blue-600 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                    <?php echo e(config('company.contact.info_email')); ?>

                </a>
                <a href="tel:<?php echo e(str_replace(' ', '', config('company.contact.phone'))); ?>" class="flex items-center hover:text-blue-600 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                    </svg>
                    <?php echo e(config('company.contact.phone')); ?>

                </a>
                <a href="<?php echo e(route('careers.index', ['locale' => app()->getLocale()])); ?>" class="flex items-center hover:text-blue-600 transition-colors <?php echo e(request()->routeIs('careers.*') ? 'text-blue-600' : ''); ?>">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v10a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012-2V8"></path>
                    </svg>
                    Careers
                </a>
            </div>
            
            <!-- Language & Currency Switchers -->
            <div class="flex items-center space-x-4">
                <!-- Language Switcher -->
                <div class="relative group">
                    <button class="flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clip-rule="evenodd"></path>
                        </svg>
                        <span><?php echo e(strtoupper(app()->getLocale())); ?></span>
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div class="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                        <a href="<?php echo e(App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('en')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 <?php echo e(app()->getLocale() === 'en' ? 'bg-blue-50 text-blue-600' : ''); ?>">English</a>
                        <a href="<?php echo e(App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('fr')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 <?php echo e(app()->getLocale() === 'fr' ? 'bg-blue-50 text-blue-600' : ''); ?>">Français</a>
                        <a href="<?php echo e(App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('es')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 <?php echo e(app()->getLocale() === 'es' ? 'bg-blue-50 text-blue-600' : ''); ?>">Español</a>
                    </div>
                </div>
                
                <!-- Currency Switcher -->
                <div class="relative group">
                    <button class="flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                        </svg>
                        <span>ZAR</span>
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div class="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                        <a href="#" onclick="changeCurrency('ZAR')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">ZAR (R)</a>
                        <a href="#" onclick="changeCurrency('USD')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">USD ($)</a>
                        <a href="#" onclick="changeCurrency('EUR')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">EUR (€)</a>
                        <a href="#" onclick="changeCurrency('GBP')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">GBP (£)</a>
                    </div>
                </div>
                
                <!-- Social Links -->
                <div class="flex items-center space-x-2">
                    <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="bg-white shadow-sm sticky top-0 z-40">
    <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16 lg:h-20">
            <?php
                use Illuminate\Support\Facades\File;

                $logoPath = public_path('images/logo.svg');
                $hasLogo = File::exists($logoPath);
            ?>

            <div class="flex-shrink-0">
                <a href="<?php echo e(route('home', ['locale' => app()->getLocale()])); ?>" class="h-12 lg:h-14 w-auto flex items-center">
                    <?php if($hasLogo): ?>
                        <img src="<?php echo e(asset('images/logo.svg')); ?>" alt="<?php echo e(__('common.company_name')); ?>" class="h-15 lg:h-17 w-auto">
                    <?php else: ?>
                        <span class="text-xl lg:text-2xl font-bold text-gray-900"><?php echo e(__('common.company_name')); ?></span>
                    <?php endif; ?>
                </a>
            </div>


            <!-- Desktop Navigation -->
            <nav class="hidden lg:flex items-center space-x-8">
                <a href="<?php echo e(route('home', ['locale' => app()->getLocale()])); ?>" class="nav-link text-gray-700 font-medium <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>">
                    <?php echo e(__('common.home')); ?>

                </a>
                <div class="relative group">
                    <a href="<?php echo e(route('services.index', ['locale' => app()->getLocale()])); ?>" class="nav-link text-gray-700 font-medium flex items-center <?php echo e(request()->routeIs('services.*') ? 'active' : ''); ?>">
                        <?php echo e(__('common.services')); ?>

                        <svg class="ml-1 w-4 h-4 transform group-hover:rotate-180 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    <!-- Services Dropdown -->
                    <div class="absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50">
                        <div class="py-3">
                            <a href="<?php echo e(route('services.ai-services', ['locale' => app()->getLocale()])); ?>" class="block px-5 py-3 text-base text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors font-medium">
                                <span class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                    <?php echo e(__('services.ai_services')); ?>

                                    <span class="ml-2 px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full">New</span>
                                </span>
                            </a>
                            <div class="border-t border-gray-100 my-2"></div>
                            <a href="<?php echo e(route('services.web-development', ['locale' => app()->getLocale()])); ?>" class="block px-5 py-3 text-base text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors font-medium">
                                <span class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                    </svg>
                                    <?php echo e(__('common.web_development')); ?>

                                </span>
                            </a>
                            <a href="<?php echo e(route('services.mobile-app-development', ['locale' => app()->getLocale()])); ?>" class="block px-5 py-3 text-base text-gray-700 hover:bg-green-50 hover:text-green-600 transition-colors font-medium">
                                <span class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"></path>
                                    </svg>
                                    <?php echo e(__('common.mobile_app_development')); ?>

                                </span>
                            </a>
                            <a href="<?php echo e(route('services.ecommerce-development', ['locale' => app()->getLocale()])); ?>" class="block px-5 py-3 text-base text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-colors font-medium">
                                <span class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                    </svg>
                                    <?php echo e(__('common.ecommerce_development')); ?>

                                </span>
                            </a>
                            <a href="<?php echo e(route('services.digital-marketing', ['locale' => app()->getLocale()])); ?>" class="block px-5 py-3 text-base text-gray-700 hover:bg-pink-50 hover:text-pink-600 transition-colors font-medium">
                                <span class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                                    </svg>
                                    <?php echo e(__('common.digital_marketing')); ?>

                                </span>
                            </a>
                            <a href="<?php echo e(route('services.data-analytics', ['locale' => app()->getLocale()])); ?>" class="block px-5 py-3 text-base text-gray-700 hover:bg-teal-50 hover:text-teal-600 transition-colors font-medium">
                                <span class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    Data Analytics
                                </span>
                            </a>
                            <a href="<?php echo e(route('services.seo-services', ['locale' => app()->getLocale()])); ?>" class="block px-5 py-3 text-base text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors font-medium">
                                <span class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    <?php echo e(__('common.seo_services')); ?>

                                </span>
                            </a>
                            <a href="<?php echo e(route('services.accounting-services', ['locale' => app()->getLocale()])); ?>" class="block px-5 py-3 text-base text-gray-700 hover:bg-yellow-50 hover:text-yellow-600 transition-colors font-medium">
                                <span class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <?php echo e(__('common.accounting_services')); ?>

                                </span>
                            </a>
                            <a href="<?php echo e(route('services.maintenance-support', ['locale' => app()->getLocale()])); ?>" class="block px-5 py-3 text-base text-gray-700 hover:bg-gray-50 hover:text-gray-600 transition-colors font-medium">
                                <span class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <?php echo e(__('common.maintenance_support')); ?>

                                </span>
                            </a>
                        </div>
                    </div>
                </div>
                <a href="<?php echo e(route('projects.index', ['locale' => app()->getLocale()])); ?>" class="nav-link text-gray-700 font-medium <?php echo e(request()->routeIs('projects.*') ? 'active' : ''); ?>">
                    <?php echo e(__('common.projects')); ?>

                </a>
                <a href="<?php echo e(route('shop.index', ['locale' => app()->getLocale()])); ?>" class="nav-link text-gray-700 font-medium <?php echo e(request()->routeIs('shop.*') ? 'active' : ''); ?>">
                    <?php echo e(__('common.shop')); ?>

                </a>
                <a href="<?php echo e(route('blog.index', ['locale' => app()->getLocale()])); ?>" class="nav-link text-gray-700 font-medium <?php echo e(request()->routeIs('blog.*') ? 'active' : ''); ?>">
                    <?php echo e(__('common.blog')); ?>

                </a>
                <a href="<?php echo e(route('about', ['locale' => app()->getLocale()])); ?>" class="nav-link text-gray-700 font-medium <?php echo e(request()->routeIs('about') ? 'active' : ''); ?>">
                    <?php echo e(__('common.about')); ?>

                </a>
                <a href="<?php echo e(route('contact', ['locale' => app()->getLocale()])); ?>" class="nav-link text-gray-700 font-medium <?php echo e(request()->routeIs('contact') ? 'active' : ''); ?>">
                    <?php echo e(__('common.contact')); ?>

                </a>
            </nav>
            
            <!-- Desktop Actions -->
            <div class="hidden lg:flex items-center space-x-4">
                <!-- Shopping Cart -->
                <a href="<?php echo e(route('cart.index', ['locale' => app()->getLocale()])); ?>" class="relative text-gray-700 hover:text-blue-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0h9"></path>
                    </svg>
                    <span class="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center cart-count hidden">0</span>
                </a>
                
                <?php if(auth()->guard()->check()): ?>
                    <!-- User Menu -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors">
                            <?php if(auth()->user()->avatar): ?>
                                <img src="<?php echo e(Storage::url(auth()->user()->avatar)); ?>" alt="<?php echo e(auth()->user()->full_name); ?>" class="w-8 h-8 rounded-full object-cover border border-gray-200">
                            <?php else: ?>
                                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-white">
                                        <?php echo e(substr(auth()->user()->first_name, 0, 1)); ?><?php echo e(substr(auth()->user()->last_name, 0, 1)); ?>

                                    </span>
                                </div>
                            <?php endif; ?>
                            <span class="font-medium"><?php echo e(auth()->user()->first_name); ?></span>
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <div class="py-2">
                                <a href="<?php echo e(auth()->user()->isAdminOrStaff() ? route('admin.dashboard') : route('dashboard')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"><?php echo e(__('common.dashboard')); ?></a>
                                <a href="<?php echo e(route('profile.edit')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Profile</a>
                                <a href="<?php echo e(route('orders.index')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"><?php echo e(__('common.my_orders')); ?></a>
                                <hr class="my-1">
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"><?php echo e(__('common.logout')); ?></button>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Auth Links -->
                    <a href="<?php echo e(route('login')); ?>" class="text-gray-700 hover:text-blue-600 font-medium transition-colors"><?php echo e(__('common.login')); ?></a>
                    <a href="<?php echo e(route('register')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"><?php echo e(__('common.register')); ?></a>
                <?php endif; ?>
            </div>
            
            <!-- Mobile Menu Button -->
            <div class="lg:hidden">
                <button id="mobile-menu-button" class="text-gray-700 hover:text-blue-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Mobile Menu -->
    <div id="mobile-menu" class="lg:hidden hidden bg-white border-t border-gray-200">
        <div class="px-4 py-4 space-y-2">
            <a href="<?php echo e(route('home', ['locale' => app()->getLocale()])); ?>" class="mobile-nav-link block px-3 py-2 rounded-lg text-gray-700 font-medium <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>"><?php echo e(__('common.home')); ?></a>

            <!-- Mobile Services Dropdown -->
            <div class="mobile-services-dropdown">
                <button onclick="toggleMobileServices()" class="mobile-nav-link w-full flex items-center justify-between px-3 py-2 rounded-lg text-gray-700 font-medium <?php echo e(request()->routeIs('services.*') ? 'active' : ''); ?>">
                    <span><?php echo e(__('common.services')); ?></span>
                    <svg id="mobile-services-arrow" class="w-4 h-4 transform transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
                <div id="mobile-services-menu" class="hidden pl-4 mt-2 space-y-1">
                    <a href="<?php echo e(route('services.ai-services', ['locale' => app()->getLocale()])); ?>" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors">
                        <svg class="w-4 h-4 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <?php echo e(__('services.ai_services')); ?>

                        <span class="ml-2 px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full">New</span>
                    </a>
                    <a href="<?php echo e(route('services.web-development', ['locale' => app()->getLocale()])); ?>" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                        <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                        <?php echo e(__('common.web_development')); ?>

                    </a>
                    <a href="<?php echo e(route('services.mobile-app-development', ['locale' => app()->getLocale()])); ?>" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"></path>
                        </svg>
                        <?php echo e(__('common.mobile_app_development')); ?>

                    </a>
                    <a href="<?php echo e(route('services.ecommerce-development', ['locale' => app()->getLocale()])); ?>" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors">
                        <svg class="w-4 h-4 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        <?php echo e(__('common.ecommerce_development')); ?>

                    </a>
                    <a href="<?php echo e(route('services.digital-marketing', ['locale' => app()->getLocale()])); ?>" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-colors">
                        <svg class="w-4 h-4 mr-2 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                        </svg>
                        <?php echo e(__('common.digital_marketing')); ?>

                    </a>
                    <a href="<?php echo e(route('services.data-analytics', ['locale' => app()->getLocale()])); ?>" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-teal-600 hover:bg-teal-50 rounded-lg transition-colors">
                        <svg class="w-4 h-4 mr-2 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Data Analytics
                    </a>
                    <a href="<?php echo e(route('services.seo-services', ['locale' => app()->getLocale()])); ?>" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors">
                        <svg class="w-4 h-4 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <?php echo e(__('common.seo_services')); ?>

                    </a>
                    <a href="<?php echo e(route('services.accounting-services', ['locale' => app()->getLocale()])); ?>" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors">
                        <svg class="w-4 h-4 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <?php echo e(__('common.accounting_services')); ?>

                    </a>
                    <a href="<?php echo e(route('services.maintenance-support', ['locale' => app()->getLocale()])); ?>" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                        <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <?php echo e(__('common.maintenance_support')); ?>

                    </a>
                </div>
            </div>

            <a href="<?php echo e(route('projects.index', ['locale' => app()->getLocale()])); ?>" class="mobile-nav-link block px-3 py-2 rounded-lg text-gray-700 font-medium <?php echo e(request()->routeIs('projects.*') ? 'active' : ''); ?>"><?php echo e(__('common.projects')); ?></a>
            <a href="<?php echo e(route('shop.index', ['locale' => app()->getLocale()])); ?>" class="mobile-nav-link block px-3 py-2 rounded-lg text-gray-700 font-medium <?php echo e(request()->routeIs('shop.*') ? 'active' : ''); ?>"><?php echo e(__('common.shop')); ?></a>
            <a href="<?php echo e(route('blog.index', ['locale' => app()->getLocale()])); ?>" class="mobile-nav-link block px-3 py-2 rounded-lg text-gray-700 font-medium <?php echo e(request()->routeIs('blog.*') ? 'active' : ''); ?>"><?php echo e(__('common.blog')); ?></a>
            <a href="<?php echo e(route('about', ['locale' => app()->getLocale()])); ?>" class="mobile-nav-link block px-3 py-2 rounded-lg text-gray-700 font-medium <?php echo e(request()->routeIs('about') ? 'active' : ''); ?>"><?php echo e(__('common.about')); ?></a>
            <a href="<?php echo e(route('contact', ['locale' => app()->getLocale()])); ?>" class="mobile-nav-link block px-3 py-2 rounded-lg text-gray-700 font-medium <?php echo e(request()->routeIs('contact') ? 'active' : ''); ?>"><?php echo e(__('common.contact')); ?></a>
            
            <?php if(auth()->guard()->check()): ?>
                <hr class="my-4">
                <a href="<?php echo e(auth()->user()->isAdminOrStaff() ? route('admin.dashboard') : route('dashboard')); ?>" class="block text-gray-700 hover:text-blue-600 font-medium transition-colors"><?php echo e(__('common.dashboard')); ?></a>
                <a href="<?php echo e(route('orders.index')); ?>" class="block text-gray-700 hover:text-blue-600 font-medium transition-colors"><?php echo e(__('common.my_orders')); ?></a>
                <form method="POST" action="<?php echo e(route('logout')); ?>">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="block text-gray-700 hover:text-blue-600 font-medium transition-colors"><?php echo e(__('common.logout')); ?></button>
                </form>
            <?php else: ?>
                <hr class="my-4">
                <a href="<?php echo e(route('login')); ?>" class="block text-gray-700 hover:text-blue-600 font-medium transition-colors"><?php echo e(__('common.login')); ?></a>
                <a href="<?php echo e(route('register')); ?>" class="block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium text-center"><?php echo e(__('common.register')); ?></a>
            <?php endif; ?>
        </div>
    </div>
</header>

<script>
    // Mobile menu toggle
    document.getElementById('mobile-menu-button').addEventListener('click', function() {
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenu.classList.toggle('hidden');
    });

    // Mobile services dropdown toggle
    function toggleMobileServices() {
        const servicesMenu = document.getElementById('mobile-services-menu');
        const arrow = document.getElementById('mobile-services-arrow');

        servicesMenu.classList.toggle('hidden');
        arrow.classList.toggle('rotate-180');
    }
</script>
<?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/partials/header.blade.php ENDPATH**/ ?>